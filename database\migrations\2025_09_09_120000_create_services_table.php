<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('service_type'); // 'sunday_morning', 'wednesday_evening', 'youth', 'special', etc.
            $table->integer('day_of_week'); // 0 = Sunday, 1 = Monday, etc.
            $table->time('start_time');
            $table->time('end_time');
            $table->boolean('is_active')->default(true);
            $table->integer('target_attendance')->nullable();
            $table->string('location')->nullable();
            $table->boolean('recurring')->default(true);
            $table->string('frequency')->default('weekly'); // weekly, monthly, special
            $table->timestamps();

            // Indexes
            $table->index('tenant_id');
            $table->index(['service_type', 'is_active']);
            $table->index(['day_of_week', 'is_active']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};

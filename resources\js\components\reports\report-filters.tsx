import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Filter, RotateCcw, Search } from 'lucide-react';

export interface FilterConfig {
    key: string;
    label: string;
    type: 'text' | 'select' | 'date' | 'daterange';
    options?: Array<{ value: string; label: string }>;
    placeholder?: string;
}

interface ReportFiltersProps {
    title?: string;
    description?: string;
    filters: Record<string, any>;
    filterConfigs: FilterConfig[];
    onFilterChange: (key: string, value: any) => void;
    onSearch: () => void;
    onReset: () => void;
    loading?: boolean;
    showResetButton?: boolean;
}

export function ReportFilters({
    title = 'Report Filters',
    description = 'Filter and search report data',
    filters,
    filterConfigs,
    onFilterChange,
    onSearch,
    onReset,
    loading = false,
    showResetButton = true,
}: ReportFiltersProps) {
    const handleReset = () => {
        // Reset all filters to their default values
        filterConfigs.forEach(config => {
            onFilterChange(config.key, '');
        });
        onReset();
    };

    const renderFilterInput = (config: FilterConfig) => {
        const value = filters[config.key] || '';

        switch (config.type) {
            case 'text':
                return (
                    <Input
                        placeholder={config.placeholder || `Enter ${config.label.toLowerCase()}`}
                        value={value}
                        onChange={(e) => onFilterChange(config.key, e.target.value)}
                    />
                );

            case 'select':
                return (
                    <Select value={value} onValueChange={(val) => onFilterChange(config.key, val)}>
                        <SelectTrigger>
                            <SelectValue placeholder={config.placeholder || `Select ${config.label.toLowerCase()}`} />
                        </SelectTrigger>
                        <SelectContent>
                            {config.options?.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                );

            case 'date':
                return (
                    <Input
                        type="date"
                        value={value}
                        onChange={(e) => onFilterChange(config.key, e.target.value)}
                    />
                );

            case 'daterange':
                // For date range, we expect the config key to be something like 'date_range'
                // and we'll handle start_date and end_date separately
                const startKey = `${config.key}_start`;
                const endKey = `${config.key}_end`;
                return (
                    <div className="grid grid-cols-2 gap-2">
                        <div>
                            <Label className="text-xs text-muted-foreground">From</Label>
                            <Input
                                type="date"
                                value={filters[startKey] || ''}
                                onChange={(e) => onFilterChange(startKey, e.target.value)}
                            />
                        </div>
                        <div>
                            <Label className="text-xs text-muted-foreground">To</Label>
                            <Input
                                type="date"
                                value={filters[endKey] || ''}
                                onChange={(e) => onFilterChange(endKey, e.target.value)}
                            />
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Filter className="h-5 w-5" />
                    {title}
                </CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
            <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {filterConfigs.map(config => (
                        <div key={config.key} className="space-y-2">
                            <Label htmlFor={config.key} className="text-sm font-medium">
                                {config.label}
                            </Label>
                            {renderFilterInput(config)}
                        </div>
                    ))}
                </div>
                
                <div className="flex items-center gap-2 mt-6">
                    <Button onClick={onSearch} disabled={loading}>
                        <Search className="mr-2 h-4 w-4" />
                        {loading ? 'Searching...' : 'Apply Filters'}
                    </Button>
                    
                    {showResetButton && (
                        <Button variant="outline" onClick={handleReset} disabled={loading}>
                            <RotateCcw className="mr-2 h-4 w-4" />
                            Reset
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

// Predefined filter configurations for common report types
export const WEEKLY_ATTENDANCE_FILTERS: FilterConfig[] = [
    {
        key: 'date_range',
        label: 'Date Range',
        type: 'daterange',
    },
    {
        key: 'service_type',
        label: 'Service Type',
        type: 'select',
        options: [
            { value: 'all', label: 'All Services' },
            { value: 'sunday_morning', label: 'Sunday Morning' },
            { value: 'wednesday_evening', label: 'Wednesday Evening' },
            { value: 'youth', label: 'Youth Service' },
            { value: 'special', label: 'Special Events' },
        ],
    },
];

export const MONTHLY_TRENDS_FILTERS: FilterConfig[] = [
    {
        key: 'year',
        label: 'Year',
        type: 'select',
        options: Array.from({ length: 5 }, (_, i) => {
            const year = new Date().getFullYear() - i;
            return { value: year.toString(), label: year.toString() };
        }),
    },
];

export const SERVICE_ATTENDANCE_FILTERS: FilterConfig[] = [
    {
        key: 'service_type',
        label: 'Service Type',
        type: 'select',
        options: [
            { value: 'all', label: 'All Services' },
            { value: 'sunday_morning', label: 'Sunday Morning' },
            { value: 'wednesday_evening', label: 'Wednesday Evening' },
            { value: 'youth', label: 'Youth Service' },
            { value: 'special', label: 'Special Events' },
        ],
    },
    {
        key: 'period',
        label: 'Period',
        type: 'select',
        options: [
            { value: 'week', label: 'This Week' },
            { value: 'month', label: 'This Month' },
            { value: 'quarter', label: 'This Quarter' },
            { value: 'year', label: 'This Year' },
        ],
    },
];

export const MEMBER_ATTENDANCE_FILTERS: FilterConfig[] = [
    {
        key: 'search',
        label: 'Search Members',
        type: 'text',
        placeholder: 'Search by name...',
    },
    {
        key: 'period',
        label: 'Period',
        type: 'select',
        options: [
            { value: 'week', label: 'This Week' },
            { value: 'month', label: 'This Month' },
            { value: 'quarter', label: 'This Quarter' },
            { value: 'year', label: 'This Year' },
        ],
    },
    {
        key: 'sort_by',
        label: 'Sort By',
        type: 'select',
        options: [
            { value: 'attendance_rate', label: 'Attendance Rate' },
            { value: 'streak', label: 'Current Streak' },
            { value: 'total_attended', label: 'Total Attended' },
            { value: 'name', label: 'Name' },
        ],
    },
    {
        key: 'sort_order',
        label: 'Order',
        type: 'select',
        options: [
            { value: 'desc', label: 'Highest First' },
            { value: 'asc', label: 'Lowest First' },
        ],
    },
];

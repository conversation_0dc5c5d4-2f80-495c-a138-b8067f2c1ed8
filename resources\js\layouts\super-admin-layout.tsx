import SuperAdminLayoutTemplate from '@/layouts/app/super-admin-sidebar-layout';
import { type BreadcrumbItem } from '@/types';
import { type ReactNode } from 'react';

interface SuperAdminLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default ({ children, breadcrumbs, ...props }: SuperAdminLayoutProps) => (
    <SuperAdminLayoutTemplate breadcrumbs={breadcrumbs} {...props}>
        {children}
    </SuperAdminLayoutTemplate>
);

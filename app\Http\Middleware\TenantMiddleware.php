<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenant = $this->resolveTenant($request);

        if (!$tenant) {
            // If no tenant found, redirect to main site or show error
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Tenant not found'], 404);
            }

            // For web requests, redirect to main registration page
            return redirect()->to(config('app.url'));
        }

        if (!$tenant->isActive()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Tenant is not active'], 403);
            }

            return response()->view('errors.tenant-inactive', compact('tenant'), 403);
        }

        // Bind tenant to the application container
        app()->instance('tenant', $tenant);

        // Set tenant context for the request
        $request->attributes->set('tenant', $tenant);

        return $next($request);
    }

    /**
     * Resolve tenant from the request.
     */
    protected function resolveTenant(Request $request): ?Tenant
    {
        // Try to resolve tenant from subdomain first
        $host = $request->getHost();

        // Check if it's a subdomain (not the main domain)
        $mainDomain = parse_url(config('app.url'), PHP_URL_HOST);

        if ($host !== $mainDomain && str_ends_with($host, '.' . $mainDomain)) {
            $subdomain = str_replace('.' . $mainDomain, '', $host);
            $tenant = Tenant::findBySlug($subdomain);

            if ($tenant) {
                return $tenant;
            }
        }

        // Try to resolve from custom domain
        $tenant = Tenant::findByDomain($host);
        if ($tenant) {
            return $tenant;
        }

        // Try to resolve from path (for development/testing)
        $pathSegments = explode('/', trim($request->path(), '/'));
        if (count($pathSegments) > 0 && $pathSegments[0] === 'tenant') {
            if (isset($pathSegments[1])) {
                return Tenant::findBySlug($pathSegments[1]);
            }
        }

        return null;
    }
}

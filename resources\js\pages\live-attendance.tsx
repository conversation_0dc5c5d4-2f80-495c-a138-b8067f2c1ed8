import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import {
    AlertCircle,
    CheckCircle,
    Clock,
    Download,
    MapPin,
    RefreshCw,
    Search,
    Settings,
    TrendingUp,
    Users,
    Wifi,
    WifiOff,
    XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Live Attendance',
        href: '/live-attendance',
    },
];

// Mock data for live attendance
interface AttendanceMember {
    id: number;
    name: string;
    avatar: string;
    status: 'present' | 'absent' | 'late';
    checkInTime?: string;
    checkOutTime?: string;
    location: 'church' | 'outside';
    department: string;
    isVolunteer: boolean;
}

const mockAttendanceData: AttendanceMember[] = [
    {
        id: 1,
        name: '<PERSON>',
        avatar: 'JS',
        status: 'present',
        checkInTime: '09:15 AM',
        location: 'church',
        department: 'Worship',
        isVolunteer: true,
    },
    {
        id: 2,
        name: '<PERSON>',
        avatar: 'SJ',
        status: 'present',
        checkInTime: '09:05 AM',
        location: 'church',
        department: 'Youth',
        isVolunteer: true,
    },
    {
        id: 3,
        name: 'Michael Brown',
        avatar: 'MB',
        status: 'late',
        checkInTime: '09:45 AM',
        location: 'church',
        department: 'Admin',
        isVolunteer: false,
    },
    {
        id: 4,
        name: 'Emily Davis',
        avatar: 'ED',
        status: 'present',
        checkInTime: '08:55 AM',
        location: 'church',
        department: 'Children',
        isVolunteer: true,
    },
    { id: 5, name: 'David Wilson', avatar: 'DW', status: 'absent', location: 'outside', department: 'Outreach', isVolunteer: false },
    {
        id: 6,
        name: 'Lisa Anderson',
        avatar: 'LA',
        status: 'present',
        checkInTime: '09:10 AM',
        location: 'church',
        department: 'Worship',
        isVolunteer: true,
    },
    {
        id: 7,
        name: 'Robert Taylor',
        avatar: 'RT',
        status: 'present',
        checkInTime: '09:20 AM',
        location: 'church',
        department: 'Finance',
        isVolunteer: false,
    },
    {
        id: 8,
        name: 'Jennifer Martinez',
        avatar: 'JM',
        status: 'present',
        checkInTime: '09:30 AM',
        location: 'church',
        department: 'Youth',
        isVolunteer: true,
    },
];

const geofenceArea = {
    name: 'Church Premises',
    count: 755,
    capacity: 1250,
    status: 'active',
};

const statusColors = {
    present: 'bg-success text-success-foreground',
    absent: 'bg-muted text-muted-foreground',
    late: 'bg-warning text-warning-foreground',
};

const locationColors = {
    church: 'bg-cyan text-white',
    outside: 'bg-muted text-muted-foreground',
};

export default function LiveAttendance() {
    const [currentTime, setCurrentTime] = useState(new Date());
    const [isLive, setIsLive] = useState(true);
    const [selectedLocation, setSelectedLocation] = useState<string>('all');
    const [searchTerm, setSearchTerm] = useState('');

    // Update current time every second
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    // Filter members based on location and search
    const filteredMembers = mockAttendanceData.filter((member) => {
        const matchesLocation = selectedLocation === 'all' || member.location === selectedLocation;
        const matchesSearch =
            member.name.toLowerCase().includes(searchTerm.toLowerCase()) || member.department.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesLocation && matchesSearch;
    });

    const totalPresent = mockAttendanceData.filter((m) => m.status === 'present').length;
    const totalLate = mockAttendanceData.filter((m) => m.status === 'late').length;
    const totalAbsent = mockAttendanceData.filter((m) => m.status === 'absent').length;
    const totalCapacity = geofenceArea.capacity;
    const totalOccupancy = geofenceArea.count;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Live Attendance" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <div className="flex items-center gap-3">
                            <h1 className="text-3xl font-bold tracking-tight">Live Attendance</h1>
                            <div className="flex items-center gap-2">
                                {isLive ? (
                                    <div className="flex items-center gap-2 text-success">
                                        <div className="h-2 w-2 animate-pulse rounded-full bg-success" />
                                        <Wifi className="h-4 w-4" />
                                        <span className="text-sm font-medium">LIVE</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2 text-muted-foreground">
                                        <WifiOff className="h-4 w-4" />
                                        <span className="text-sm font-medium">OFFLINE</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        <p className="text-muted-foreground">Real-time attendance monitoring • {currentTime.toLocaleTimeString()}</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                            <Settings className="mr-2 h-4 w-4" />
                            Geofence Settings
                        </Button>
                        <Button variant="outline" size="sm">
                            <Download className="mr-2 h-4 w-4" />
                            Export Data
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => setIsLive(!isLive)}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            {isLive ? 'Pause' : 'Resume'}
                        </Button>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Total Present</p>
                                    <p className="text-2xl font-bold text-success">{totalPresent}</p>
                                    <div className="flex items-center gap-1">
                                        <TrendingUp className="h-4 w-4 text-success" />
                                        <span className="text-sm font-medium text-success">+12 from last week</span>
                                    </div>
                                </div>
                                <div className="rounded-lg bg-success p-3 text-white">
                                    <CheckCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Late Arrivals</p>
                                    <p className="text-2xl font-bold text-warning">{totalLate}</p>
                                    <p className="text-sm text-muted-foreground">Arrived after 9:30 AM</p>
                                </div>
                                <div className="rounded-lg bg-warning p-3 text-white">
                                    <AlertCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Absent</p>
                                    <p className="text-2xl font-bold text-muted-foreground">{totalAbsent}</p>
                                    <p className="text-sm text-muted-foreground">Expected but not present</p>
                                </div>
                                <div className="rounded-lg bg-muted p-3 text-muted-foreground">
                                    <XCircle className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Capacity</p>
                                    <p className="text-2xl font-bold">{Math.round((totalOccupancy / totalCapacity) * 100)}%</p>
                                    <p className="text-sm text-muted-foreground">
                                        {totalOccupancy} / {totalCapacity}
                                    </p>
                                </div>
                                <div className="rounded-lg bg-cyan p-3 text-white">
                                    <Users className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Church Geofence Status */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="mb-6">
                            <h3 className="text-lg font-semibold">Church Geofence Status</h3>
                            <p className="text-sm text-muted-foreground">Single-zone attendance monitoring</p>
                        </div>
                        <div className="max-w-md">
                            <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <MapPin className="h-4 w-4 text-cyan" />
                                        <span className="font-medium">{geofenceArea.name}</span>
                                    </div>
                                    <Badge variant="outline" className="text-xs">
                                        {geofenceArea.status}
                                    </Badge>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span>{geofenceArea.count} people present</span>
                                        <span className="text-muted-foreground">
                                            {Math.round((geofenceArea.count / geofenceArea.capacity) * 100)}%
                                        </span>
                                    </div>
                                    <div className="h-3 w-full rounded-full bg-muted">
                                        <div
                                            className="h-3 rounded-full bg-cyan transition-all duration-500"
                                            style={{ width: `${(geofenceArea.count / geofenceArea.capacity) * 100}%` }}
                                        />
                                    </div>
                                    <p className="text-xs text-muted-foreground">Total Capacity: {geofenceArea.capacity}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Live Member List */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h3 className="text-lg font-semibold">Live Member Status</h3>
                                <p className="text-sm text-muted-foreground">Real-time check-in/check-out tracking</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                    <input
                                        type="text"
                                        placeholder="Search members..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="rounded-md border border-input bg-background py-2 pr-4 pl-10 text-sm"
                                    />
                                </div>
                                <select
                                    value={selectedLocation}
                                    onChange={(e) => setSelectedLocation(e.target.value)}
                                    className="rounded-md border border-input bg-background px-3 py-2 text-sm"
                                >
                                    <option value="all">All Members</option>
                                    <option value="church">In Church</option>
                                    <option value="outside">Outside Church</option>
                                </select>
                            </div>
                        </div>

                        <div className="space-y-3">
                            {filteredMembers.map((member) => (
                                <div
                                    key={member.id}
                                    className="flex items-center justify-between rounded-lg border border-border p-4 transition-colors hover:bg-muted/50"
                                >
                                    <div className="flex items-center gap-4">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-cyan font-medium text-white">
                                            {member.avatar}
                                        </div>
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <span className="font-medium">{member.name}</span>
                                                {member.isVolunteer && (
                                                    <Badge variant="outline" className="text-xs">
                                                        Volunteer
                                                    </Badge>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground">{member.department}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-4">
                                        <div className="text-right">
                                            {member.checkInTime && (
                                                <div className="flex items-center gap-1 text-sm">
                                                    <Clock className="h-3 w-3" />
                                                    <span>{member.checkInTime}</span>
                                                </div>
                                            )}
                                            {member.checkOutTime && (
                                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                                    <span>Out: {member.checkOutTime}</span>
                                                </div>
                                            )}
                                        </div>

                                        <Badge className={locationColors[member.location]}>
                                            {member.location === 'church' ? 'In Church' : 'Outside Church'}
                                        </Badge>

                                        <Badge className={statusColors[member.status]}>
                                            {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                                        </Badge>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </Card>
            </div>
        </AppLayout>
    );
}

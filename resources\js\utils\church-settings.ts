/**
 * Validation functions for church settings
 */

export const validateLatitude = (lat: number): string | undefined => {
    if (isNaN(lat)) return 'Please enter a valid number';
    if (lat < -90 || lat > 90) return 'Latitude must be between -90 and 90 degrees';
    return undefined;
};

export const validateLongitude = (lng: number): string | undefined => {
    if (isNaN(lng)) return 'Please enter a valid number';
    if (lng < -180 || lng > 180) return 'Longitude must be between -180 and 180 degrees';
    return undefined;
};

export const validateGeofenceRadius = (radius: number): string | undefined => {
    if (isNaN(radius)) return 'Please enter a valid number';
    if (radius < 10) return 'Minimum radius is 10 meters';
    if (radius > 10000) return 'Maximum radius is 10,000 meters';
    return undefined;
};

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param lat1 First latitude
 * @param lon1 First longitude
 * @param lat2 Second latitude
 * @param lon2 Second longitude
 * @returns Distance in meters
 */
export const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
};

/**
 * Test geofence functionality using browser geolocation
 * @param churchLat Church latitude
 * @param churchLng Church longitude
 * @param geofenceRadius Geofence radius in meters
 */
export const testGeofence = (churchLat: number, churchLng: number, geofenceRadius: number): void => {
    if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const distance = calculateDistance(
                    position.coords.latitude,
                    position.coords.longitude,
                    churchLat,
                    churchLng,
                );

                const isWithinGeofence = distance <= geofenceRadius;
                alert(`You are ${distance.toFixed(0)}m from the church. ${isWithinGeofence ? 'Within' : 'Outside'} geofence.`);
            },
            (error) => {
                alert('Unable to get your location: ' + error.message);
            },
        );
    } else {
        alert('Geolocation is not supported by this browser.');
    }
};

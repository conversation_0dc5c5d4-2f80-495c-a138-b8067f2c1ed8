<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChurchRegistrationRequest;
use App\Models\ChurchSetting;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class ChurchRegistrationController extends Controller
{
    /**
     * Show the church registration form.
     */
    public function create()
    {
        return Inertia::render('ChurchRegistration', [
            'suggestedSlug' => $this->generateSlugSuggestion(),
        ]);
    }

    /**
     * Handle church registration.
     */
    public function store(ChurchRegistrationRequest $request)
    {
        try {
            DB::beginTransaction();

            // Create the tenant (church)
            $tenant = Tenant::create([
                'name' => $request->church_name,
                'slug' => $request->slug ?: null, // Will be auto-generated if empty
                'status' => 'active',
                'plan_type' => 'basic',
            ]);

            // Create the admin user for this tenant
            $adminUser = User::create([
                'name' => $request->admin_name,
                'email' => $request->admin_email,
                'password' => Hash::make($request->admin_password),
                'tenant_id' => $tenant->id,
                'email_verified_at' => now(), // Auto-verify for simplicity
            ]);

            // Create default church settings for this tenant
            ChurchSetting::create([
                'tenant_id' => $tenant->id,
                'church_name' => $request->church_name,
                'church_email' => $request->admin_email,
            ]);

            DB::commit();

            // Log in the new admin user
            Auth::login($adminUser);

            // Redirect to the tenant's subdomain dashboard
            $tenantUrl = $this->getTenantUrl($tenant);

            return redirect()->to($tenantUrl . '/dashboard')
                ->with('success', 'Welcome to Flockin! Your church account has been created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors([
                'registration' => 'An error occurred during registration. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Check if a slug is available.
     */
    public function checkSlug(Request $request)
    {
        $slug = $request->input('slug');

        if (empty($slug)) {
            return response()->json(['available' => false, 'message' => 'Slug is required']);
        }

        $available = !Tenant::where('slug', $slug)->exists();

        return response()->json([
            'available' => $available,
            'message' => $available ? 'This URL is available!' : 'This URL is already taken.'
        ]);
    }

    /**
     * Generate a slug suggestion based on common church names.
     */
    protected function generateSlugSuggestion(): string
    {
        $suggestions = [
            'grace-church',
            'hope-fellowship',
            'faith-community',
            'new-life-church',
            'victory-church',
            'cornerstone-church',
            'harvest-church',
            'lighthouse-church',
            'crossroads-church',
            'bridge-church'
        ];

        do {
            $suggestion = $suggestions[array_rand($suggestions)] . '-' . rand(100, 999);
        } while (Tenant::where('slug', $suggestion)->exists());

        return $suggestion;
    }

    /**
     * Get the full tenant URL.
     */
    protected function getTenantUrl(Tenant $tenant): string
    {
        $baseUrl = config('app.url');
        $parsedUrl = parse_url($baseUrl);

        // For development, use path-based routing
        if (app()->environment('local')) {
            return $baseUrl . '/tenant/' . $tenant->slug;
        }

        // For production, use subdomain
        return $parsedUrl['scheme'] . '://' . $tenant->slug . '.' . $parsedUrl['host'];
    }
}

# Multi-Tenancy Architecture Design for Flockin v2

## Overview
This document outlines the comprehensive multi-tenancy architecture for supporting 1000+ church tenants with complete data isolation and scalable infrastructure.

## Architecture Strategy

### 1. Tenant Identification Method
**Selected Approach: Subdomain-based Routing**
- Each church gets a unique subdomain: `{church-slug}.flockin.app`
- Benefits: Clean URLs, better branding, easy tenant identification
- Fallback: Path-based routing for development/testing

### 2. Data Isolation Strategy
**Selected Approach: Single Database with Tenant ID Column**
- Add `tenant_id` to all tenant-specific tables
- Use Laravel Global Scopes for automatic query filtering
- Benefits: Cost-effective, easier maintenance, better performance for 1000 tenants
- Alternative considered: Separate databases (rejected due to complexity and cost)

### 3. Database Schema Changes

#### New Tables
```sql
-- Tenants table (churches)
tenants (
    id, 
    name, 
    slug (unique), 
    domain (nullable), 
    database_name (nullable),
    status (active/inactive/suspended),
    plan_type,
    created_at,
    updated_at
)

-- Tenant Users (pivot table for super admin access)
tenant_users (
    id,
    tenant_id,
    user_id,
    role (admin/super_admin/member),
    created_at,
    updated_at
)
```

#### Modified Tables
All existing tables will get `tenant_id` column:
- users (add tenant_id, make email unique per tenant)
- church_settings (remove singleton pattern, add tenant_id)
- services (add tenant_id)
- attendance_records (add tenant_id)
- hymns (add tenant_id)
- devotionals (add tenant_id)
- videos (add tenant_id)
- content_categories (add tenant_id)

### 4. Authentication & Authorization

#### Multi-Tenant Authentication
- Users belong to specific tenants
- Email uniqueness scoped to tenant (same email can exist across tenants)
- Super admins can access multiple tenants

#### Authorization Levels
1. **Super Admin**: Cross-tenant access, system management
2. **Tenant Admin**: Full access within their tenant
3. **Tenant Member**: Limited access within their tenant

### 5. Middleware Architecture

#### TenantMiddleware
- Identifies tenant from subdomain/path
- Sets tenant context in application
- Applies global scopes to models
- Handles tenant not found scenarios

#### SuperAdminMiddleware
- Allows super admins to switch between tenants
- Bypasses tenant restrictions for system management

### 6. Model Architecture

#### Base Tenant Model
```php
abstract class TenantModel extends Model
{
    protected static function booted()
    {
        static::addGlobalScope(new TenantScope);
        
        static::creating(function ($model) {
            $model->tenant_id = app('tenant')->id;
        });
    }
}
```

#### Tenant Scope
```php
class TenantScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (app()->bound('tenant') && !app('tenant.bypass')) {
            $builder->where('tenant_id', app('tenant')->id);
        }
    }
}
```

### 7. Church Registration Flow

#### Public Registration Process
1. Church visits registration page (no subdomain)
2. Fills minimal form: church name, admin name, email, password
3. System generates unique slug from church name
4. Creates tenant record and admin user
5. Redirects to `{slug}.flockin.app/dashboard`
6. Shows onboarding wizard for additional setup

#### Tenant Provisioning
- Automatic subdomain setup
- Default church settings creation
- Admin user creation with tenant association
- Welcome email with setup instructions

### 8. Super Admin System

#### Super Admin Features
- Cross-tenant dashboard
- Tenant management (create, suspend, delete)
- System-wide analytics
- User impersonation
- Billing management (future)

#### Super Admin Seeder
```php
// Creates super admin with specified credentials
User::create([
    'name' => 'Super Admin',
    'email' => '<EMAIL>',
    'password' => Hash::make('Gilash@123'),
    'is_super_admin' => true,
    'tenant_id' => null // Super admins don't belong to specific tenants
]);
```

### 9. Frontend Architecture

#### Tenant Context
- React context provider for tenant information
- Automatic API calls with tenant context
- Tenant-specific branding and settings

#### Routing Strategy
- Subdomain detection in frontend
- Tenant-aware route generation
- Fallback handling for invalid tenants

### 10. Security Considerations

#### Data Isolation
- Global scopes prevent cross-tenant data access
- Middleware validates tenant access
- Database constraints ensure data integrity

#### Authentication Security
- Tenant-scoped password resets
- Session isolation between tenants
- CSRF protection per tenant

### 11. Performance Optimizations

#### Database Indexing
- Composite indexes on (tenant_id, frequently_queried_columns)
- Separate indexes for super admin queries

#### Caching Strategy
- Tenant-specific cache keys
- Redis namespace per tenant
- Shared cache for system-wide data

### 12. Migration Strategy

#### Existing Data Migration
1. Create tenants table and default tenant
2. Add tenant_id columns to existing tables
3. Populate tenant_id with default tenant ID
4. Update models with tenant scopes
5. Test data isolation thoroughly

#### Rollback Plan
- Keep backup of pre-migration database
- Ability to remove tenant_id columns
- Revert model changes if needed

## Implementation Phases

1. **Core Infrastructure**: Tenant model, middleware, scopes
2. **Database Migration**: Add tenant_id columns, migrate existing data
3. **Authentication Updates**: Multi-tenant auth, super admin system
4. **Model Updates**: Apply tenant scopes to all models
5. **Controller Updates**: Ensure tenant-aware operations
6. **Frontend Updates**: Tenant context, subdomain routing
7. **Registration System**: Public church registration flow
8. **Testing**: Comprehensive tenant isolation testing
9. **Documentation**: Update API docs, deployment guides

## Success Metrics

- Complete data isolation between tenants
- Sub-100ms response times for tenant operations
- Support for 1000+ concurrent tenants
- Zero cross-tenant data leaks
- Seamless church onboarding experience
- Super admin efficiency tools

## Risk Mitigation

- Comprehensive testing of tenant isolation
- Database backup before major migrations
- Gradual rollout with monitoring
- Rollback procedures documented
- Performance monitoring and alerting

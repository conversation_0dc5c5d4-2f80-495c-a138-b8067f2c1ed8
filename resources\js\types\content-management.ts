import { LucideIcon } from 'lucide-react';

// ===== BASE INTERFACES =====

export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string;
    created_at: string;
    updated_at: string;
}

export interface BaseContent {
    id: number;
    title: string;
    slug: string;
    status: 'draft' | 'published' | 'archived';
    is_featured: boolean;
    published_at?: string;
    created_by: number;
    updated_by?: number;
    created_at: string;
    updated_at: string;
    creator?: User;
    updater?: User;
}

// ===== CONTENT CATEGORY =====

export interface ContentCategory {
    id: number;
    name: string;
    slug: string;
    description?: string;
    type: 'video' | 'hymn' | 'devotional' | 'general';
    color: string;
    icon?: string;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

export interface ContentCategoryFormData {
    name: string;
    slug?: string;
    description?: string;
    type: 'video' | 'hymn' | 'devotional' | 'general';
    color?: string;
    icon?: string;
    is_active: boolean;
    sort_order: number;
}

// ===== VIDEO CONTENT =====

export interface Video extends BaseContent {
    description?: string;
    speaker?: string;
    recorded_date?: string;
    scripture_references?: string[];
    type: 'sermon' | 'testimony' | 'announcement' | 'event' | 'other';
    file_path: string;
    thumbnail_path?: string;
    original_filename: string;
    mime_type: string;
    file_size: number;
    duration?: number;
    video_metadata?: Record<string, any>;
    category_id?: number;
    series_name?: string;
    series_order?: number;
    view_count: number;
    download_count: number;
    category?: ContentCategory;
    video_url?: string;
    thumbnail_url?: string;
    formatted_file_size?: string;
    formatted_duration?: string;
}

export interface VideoFormData {
    title: string;
    slug?: string;
    description?: string;
    speaker?: string;
    recorded_date?: string;
    scripture_references?: string[];
    type: 'sermon' | 'testimony' | 'announcement' | 'event' | 'other';
    video_file?: File;
    thumbnail?: File;
    category_id?: number;
    series_name?: string;
    series_order?: number;
    status: 'draft' | 'published' | 'archived';
    is_featured: boolean;
    published_at?: string;
}

// ===== HYMN CONTENT =====

export interface Hymn extends BaseContent {
    author?: string;
    composer?: string;
    year_written?: number;
    lyrics: string;
    verse_structure?: Record<string, any>;
    type: 'traditional' | 'contemporary' | 'seasonal' | 'special';
    key_signature?: string;
    time_signature: string;
    tempo_bpm?: number;
    chord_progression?: string;
    chord_chart_path?: string;
    sheet_music_path?: string;
    audio_path?: string;
    category_id?: number;
    themes?: string[];
    scripture_references?: string[];
    usage_count: number;
    last_used_at?: string;
    category?: ContentCategory;
    chord_chart_url?: string;
    sheet_music_url?: string;
    audio_url?: string;
    formatted_lyrics?: string;
}

export interface HymnFormData {
    title: string;
    slug?: string;
    author?: string;
    composer?: string;
    year_written?: number;
    lyrics: string;
    verse_structure?: Record<string, any>;
    type: 'traditional' | 'contemporary' | 'seasonal' | 'special';
    key_signature?: string;
    time_signature?: string;
    tempo_bpm?: number;
    chord_progression?: string;
    chord_chart?: File;
    sheet_music?: File;
    audio_file?: File;
    category_id?: number;
    themes?: string[];
    scripture_references?: string[];
    status: 'draft' | 'published' | 'archived';
    is_featured: boolean;
    published_at?: string;
}

// ===== DEVOTIONAL CONTENT =====

export interface Devotional extends BaseContent {
    content: string;
    summary?: string;
    author?: string;
    devotional_date?: string;
    scripture_references?: string[];
    key_verse?: string;
    key_verse_reference?: string;
    prayer?: string;
    reflection_questions?: string;
    category_id?: number;
    themes?: string[];
    series_name?: string;
    series_order?: number;
    featured_image_path?: string;
    audio_path?: string;
    scheduled_for?: string;
    view_count: number;
    share_count: number;
    tags?: string[];
    category?: ContentCategory;
    featured_image_url?: string;
    audio_url?: string;
    formatted_content?: string;
    excerpt?: string;
}

export interface DevotionalFormData {
    title: string;
    slug?: string;
    content: string;
    summary?: string;
    author?: string;
    devotional_date?: string;
    scripture_references?: string[];
    key_verse?: string;
    key_verse_reference?: string;
    prayer?: string;
    reflection_questions?: string;
    category_id?: number;
    themes?: string[];
    series_name?: string;
    series_order?: number;
    featured_image?: File;
    audio_file?: File;
    status: 'draft' | 'published' | 'scheduled' | 'archived';
    is_featured: boolean;
    published_at?: string;
    scheduled_for?: string;
    tags?: string[];
}

// ===== API RESPONSES =====

export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}

export interface PaginatedResponse<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
    links: {
        first: string;
        last: string;
        prev?: string;
        next?: string;
    };
}

export interface ContentStats {
    videos: {
        total: number;
        published: number;
        draft: number;
        featured: number;
    };
    hymns: {
        total: number;
        published: number;
        draft: number;
        featured: number;
    };
    devotionals: {
        total: number;
        published: number;
        draft: number;
        scheduled: number;
        featured: number;
    };
    categories: {
        total: number;
        active: number;
    };
}

export interface SearchResults {
    videos: Video[];
    hymns: Hymn[];
    devotionals: Devotional[];
}

// ===== FILTER AND SORT OPTIONS =====

export interface FilterOptions {
    category_id?: number;
    type?: string;
    status?: string;
    search?: string;
    date_from?: string;
    date_to?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
    per_page?: number;
    page?: number;
}

// ===== COMPONENT PROPS =====

export interface ContentManagementPageProps {
    categories: ContentCategory[];
    videos: Video[];
    hymns: Hymn[];
    devotionals: Devotional[];
    stats: ContentStats;
}

export interface TabConfig {
    key: string;
    label: string;
    icon: LucideIcon;
    count?: number;
}

export interface FileUploadProgress {
    file: File;
    progress: number;
    status: 'uploading' | 'completed' | 'error';
    error?: string;
}

// ===== FORM COMPONENT PROPS =====

export interface BaseFormProps<T> {
    data: T;
    errors: Record<string, string>;
    processing: boolean;
    onSubmit: (data: T) => void;
    onCancel: () => void;
}

export interface VideoFormProps extends BaseFormProps<VideoFormData> {
    categories: ContentCategory[];
    uploadProgress?: FileUploadProgress[];
}

export interface HymnFormProps extends BaseFormProps<HymnFormData> {
    categories: ContentCategory[];
    uploadProgress?: FileUploadProgress[];
}

export interface DevotionalFormProps extends BaseFormProps<DevotionalFormData> {
    categories: ContentCategory[];
    uploadProgress?: FileUploadProgress[];
}

export interface CategoryFormProps extends BaseFormProps<ContentCategoryFormData> {
    //
}

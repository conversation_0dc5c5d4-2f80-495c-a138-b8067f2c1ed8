import { AttendanceSettingsForm } from '@/components/church/attendance-settings-form';
import { ChurchInformationForm } from '@/components/church/church-information-form';
import { IntegrationSettingsForm } from '@/components/church/integration-settings-form';
import { LocationSettingsForm } from '@/components/church/location-settings-form';
import { NotificationSettingsForm } from '@/components/church/notification-settings-form';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { type ChurchSettings, type ChurchSettingsPageProps, type CoordinateErrors } from '@/types/church-settings';
import { calculateDistance, testGeofence, validateGeofenceRadius, validateLatitude, validateLongitude } from '@/utils/church-settings';
import { Head, router } from '@inertiajs/react';
import { Bell, Building, MapPin, Smartphone, Users } from 'lucide-react';
import { useState } from 'react';



const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Church Settings', href: '/church-settings' },
];

export default function ChurchSettingsPage({ settings }: ChurchSettingsPageProps) {
    const [formData, setFormData] = useState<ChurchSettings>({
        church_name: settings?.church_name || '',
        church_address: settings?.church_address || '',
        church_phone: settings?.church_phone || '',
        church_email: settings?.church_email || '',
        church_website: settings?.church_website || '',
        social_media_links: settings?.social_media_links || {},
        church_description: settings?.church_description || '',
        mission_statement: settings?.mission_statement || '',
        service_times: settings?.service_times || [{ day: '', time: '', service_type: '' }],
        leadership_info: settings?.leadership_info || [{ name: '', position: '', bio: '' }],
        latitude: settings?.latitude || 0,
        longitude: settings?.longitude || 0,
        geofence_radius: settings?.geofence_radius || 100,
        attendance_preferences: settings?.attendance_preferences || {
            auto_checkin: true,
            notification_enabled: true,
            late_threshold: 30,
        },
        manual_checkin_settings: settings?.manual_checkin_settings || {
            enabled: true,
            admin_only: false,
            time_window: 120,
        },
        notification_settings: settings?.notification_settings || {
            email_enabled: true,
            sms_enabled: false,
            push_enabled: true,
        },
        mobile_app_config: settings?.mobile_app_config || {
            app_name: 'Church App',
            theme_color: '#34B0E0',
            notification_enabled: true,
        },
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [coordinateErrors, setCoordinateErrors] = useState<CoordinateErrors>({});

    const handleCoordinateChange = (field: 'latitude' | 'longitude' | 'geofence_radius', value: string) => {
        const numValue = parseFloat(value);

        // Clear previous error for this field
        setCoordinateErrors((prev) => ({
            ...prev,
            [field]: undefined,
        }));

        // Validate the input
        let error: string | undefined;
        if (field === 'latitude') {
            error = validateLatitude(numValue);
        } else if (field === 'longitude') {
            error = validateLongitude(numValue);
        } else if (field === 'geofence_radius') {
            error = validateGeofenceRadius(numValue);
        }

        // Set error if validation failed
        if (error) {
            setCoordinateErrors((prev) => ({
                ...prev,
                [field]: error,
            }));
        }

        // Update form data
        setFormData((prev) => ({
            ...prev,
            [field]: isNaN(numValue) ? 0 : numValue,
        }));
    };

    const handleInputChange = (field: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleNestedChange = (parent: string, field: string, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [parent]: {
                ...(prev[parent as keyof ChurchSettings] as object),
                [field]: value,
            },
        }));
    };

    const handleServiceTimeChange = (index: number, field: string, value: string) => {
        const newServiceTimes = [...(formData.service_times || [])];
        newServiceTimes[index] = { ...newServiceTimes[index], [field]: value };
        setFormData((prev) => ({
            ...prev,
            service_times: newServiceTimes,
        }));
    };

    const addServiceTime = () => {
        setFormData((prev) => ({
            ...prev,
            service_times: [...(prev.service_times || []), { day: '', time: '', service_type: '' }],
        }));
    };

    const removeServiceTime = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            service_times: prev.service_times?.filter((_, i) => i !== index) || [],
        }));
    };

    const handleLeadershipChange = (index: number, field: string, value: string) => {
        const newLeadership = [...(formData.leadership_info || [])];
        newLeadership[index] = { ...newLeadership[index], [field]: value };
        setFormData((prev) => ({
            ...prev,
            leadership_info: newLeadership,
        }));
    };

    const addLeadership = () => {
        setFormData((prev) => ({
            ...prev,
            leadership_info: [...(prev.leadership_info || []), { name: '', position: '', bio: '' }],
        }));
    };

    const removeLeadership = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            leadership_info: prev.leadership_info?.filter((_, i) => i !== index) || [],
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setShowConfirmDialog(true);
    };

    const confirmSubmit = () => {
        setIsSubmitting(true);
        router.put('/church-settings', formData as any, {
            onSuccess: () => {
                setIsSubmitting(false);
                setShowConfirmDialog(false);
            },
            onError: () => {
                setIsSubmitting(false);
                setShowConfirmDialog(false);
            },
        });
    };

    const handleTestGeofence = () => {
        testGeofence(formData.latitude || 0, formData.longitude || 0, formData.geofence_radius || 100);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Church Settings" />

            <div className="flex-1 space-y-6 p-6">
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold tracking-tight">Church Settings</h1>
                    <p className="text-muted-foreground">Configure your church information, location, and system preferences.</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                    <Tabs defaultValue="general" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-5">
                            <TabsTrigger value="general" className="flex items-center gap-2">
                                <Building className="h-4 w-4" />
                                General
                            </TabsTrigger>
                            <TabsTrigger value="location" className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                Location
                            </TabsTrigger>
                            <TabsTrigger value="attendance" className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                Attendance
                            </TabsTrigger>
                            <TabsTrigger value="notifications" className="flex items-center gap-2">
                                <Bell className="h-4 w-4" />
                                Notifications
                            </TabsTrigger>
                            <TabsTrigger value="integration" className="flex items-center gap-2">
                                <Smartphone className="h-4 w-4" />
                                Integration
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="general" className="space-y-6">
                            <ChurchInformationForm
                                formData={formData}
                                onInputChange={handleInputChange}
                                onNestedChange={handleNestedChange}
                                onServiceTimeChange={handleServiceTimeChange}
                                onAddServiceTime={addServiceTime}
                                onRemoveServiceTime={removeServiceTime}
                                onLeadershipChange={handleLeadershipChange}
                                onAddLeadership={addLeadership}
                                onRemoveLeadership={removeLeadership}
                            />
                        </TabsContent>

                        <TabsContent value="location" className="space-y-6">
                            <LocationSettingsForm
                                formData={formData}
                                coordinateErrors={coordinateErrors}
                                onInputChange={handleInputChange}
                                onNestedChange={handleNestedChange}
                                onCoordinateChange={handleCoordinateChange}
                                onTestGeofence={handleTestGeofence}
                            />
                        </TabsContent>

                        <TabsContent value="attendance" className="space-y-6">
                            <AttendanceSettingsForm
                                formData={formData}
                                onInputChange={handleInputChange}
                                onNestedChange={handleNestedChange}
                            />
                        </TabsContent>

                        <TabsContent value="notifications" className="space-y-6">
                            <NotificationSettingsForm
                                formData={formData}
                                onInputChange={handleInputChange}
                                onNestedChange={handleNestedChange}
                            />
                        </TabsContent>

                        <TabsContent value="integration" className="space-y-6">
                            <IntegrationSettingsForm
                                formData={formData}
                                onInputChange={handleInputChange}
                                onNestedChange={handleNestedChange}
                            />
                        </TabsContent>
                    </Tabs>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Button type="button" variant="outline" onClick={() => router.visit('/dashboard')}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? 'Saving...' : 'Save Settings'}
                        </Button>
                    </div>
                </form>

                {/* Confirmation Dialog */}
                <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Confirm Settings Update</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <p>Are you sure you want to save these church settings? This will update your church configuration.</p>
                            <div className="flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={confirmSubmit} disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Confirm Save'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}

<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class NotificationsController extends Controller
{
    /**
     * Display the notifications management page.
     */
    public function index(): Response
    {
        $members = User::select('id', 'name', 'email')
            ->orderBy('name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => '+1 (555) 123-456' . $user->id, // Mock phone number
                    'group' => $this->getRandomGroup(), // Mock group assignment
                ];
            });

        return Inertia::render('notifications', [
            'members' => $members,
            'templates' => $this->getNotificationTemplates(),
            'recentNotifications' => [], // TODO: Get from notifications history
        ]);
    }

    /**
     * Send notification to selected members.
     */
    public function send(Request $request)
    {
        $request->validate([
            'type' => 'required|in:sms,email,push,announcement',
            'recipients' => 'required|array',
            'recipients.*' => 'integer|exists:users,id',
            'subject' => 'required_if:type,email|string|max:255',
            'message' => 'required|string',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        // TODO: Implement notification sending logic
        // This would integrate with SMS gateway, email service, push notification service

        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully',
        ]);
    }

    /**
     * Schedule notification for future delivery.
     */
    public function schedule(Request $request)
    {
        $request->validate([
            'type' => 'required|in:sms,email,push,announcement',
            'recipients' => 'required|array',
            'message' => 'required|string',
            'scheduled_at' => 'required|date|after:now',
        ]);

        // TODO: Implement notification scheduling logic

        return response()->json([
            'success' => true,
            'message' => 'Notification scheduled successfully',
        ]);
    }

    /**
     * Get notification history.
     */
    public function history()
    {
        // TODO: Get notification history from database
        return response()->json([
            'notifications' => [],
        ]);
    }

    /**
     * Get predefined notification templates.
     */
    private function getNotificationTemplates(): array
    {
        return [
            'welcome' => [
                'subject' => 'Welcome to our Church Community',
                'message' => 'Welcome to our church family! We\'re excited to have you join us in worship and fellowship.',
            ],
            'birthday' => [
                'subject' => 'Happy Birthday!',
                'message' => 'Wishing you a blessed birthday filled with joy, love, and God\'s grace.',
            ],
            'service_reminder' => [
                'subject' => 'Service Reminder',
                'message' => 'Don\'t forget about our upcoming service this Sunday. See you there!',
            ],
            'event_announcement' => [
                'subject' => 'Church Event Announcement',
                'message' => 'We have an exciting event coming up! Join us for fellowship and fun.',
            ],
            'prayer_request' => [
                'subject' => 'Prayer Request Update',
                'message' => 'We wanted to update you on our church prayer requests and needs.',
            ],
            'donation_thanks' => [
                'subject' => 'Thank You for Your Generosity',
                'message' => 'Thank you for your generous donation. Your support helps us continue our ministry.',
            ],
        ];
    }

    /**
     * Get a random group for mock data.
     */
    private function getRandomGroup(): string
    {
        $groups = [
            'Adult Ministry',
            'Youth Ministry',
            'Children Ministry',
            'Senior Ministry',
            'Worship Team',
            'Prayer Group',
            'Bible Study',
        ];

        return $groups[array_rand($groups)];
    }
}

import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { useState, useMemo } from 'react';
import { Search, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Members',
        href: '/members',
    },
];

// Member data type
interface Member {
    id: number;
    name: string;
    email: string;
    phone: string;
    status: 'active' | 'inactive' | 'pending';
    joinDate: string;
    department: string;
}

// Sample member data
const memberData: Member[] = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-01-15', department: 'Worship' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-02-20', department: 'Youth' },
    { id: 3, name: 'Michael Brown', email: '<EMAIL>', phone: '+****************', status: 'inactive', joinDate: '2022-11-10', department: 'Admin' },
    { id: 4, name: 'Emily Davis', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-03-05', department: 'Children' },
    { id: 5, name: 'David Wilson', email: '<EMAIL>', phone: '+****************', status: 'pending', joinDate: '2023-12-01', department: 'Outreach' },
    { id: 6, name: 'Lisa Anderson', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-04-12', department: 'Worship' },
    { id: 7, name: 'Robert Taylor', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-05-18', department: 'Finance' },
    { id: 8, name: 'Jennifer Martinez', email: '<EMAIL>', phone: '+****************', status: 'inactive', joinDate: '2022-09-22', department: 'Youth' },
    { id: 9, name: 'Christopher Lee', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-06-30', department: 'Tech' },
    { id: 10, name: 'Amanda White', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-07-14', department: 'Children' },
    { id: 11, name: 'James Garcia', email: '<EMAIL>', phone: '+****************', status: 'pending', joinDate: '2023-11-28', department: 'Outreach' },
    { id: 12, name: 'Michelle Rodriguez', email: '<EMAIL>', phone: '+****************', status: 'active', joinDate: '2023-08-09', department: 'Admin' },
];

type SortField = keyof Member;
type SortDirection = 'asc' | 'desc' | null;

const statusColors = {
    active: 'bg-success text-success-foreground',
    inactive: 'bg-muted text-muted-foreground',
    pending: 'bg-warning text-warning-foreground',
};

export default function Members() {
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [sortField, setSortField] = useState<SortField>('name');
    const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

    // Filter and sort data
    const filteredAndSortedData = useMemo(() => {
        let filtered = memberData.filter(member =>
            member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            member.department.toLowerCase().includes(searchTerm.toLowerCase())
        );

        if (sortDirection && sortField) {
            filtered.sort((a, b) => {
                const aValue = a[sortField];
                const bValue = b[sortField];
                
                if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
                if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }

        return filtered;
    }, [searchTerm, sortField, sortDirection]);

    // Pagination
    const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedData = filteredAndSortedData.slice(startIndex, startIndex + itemsPerPage);

    const handleSort = (field: SortField) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const getSortIcon = (field: SortField) => {
        if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
        if (sortDirection === 'asc') return <ArrowUp className="h-4 w-4" />;
        if (sortDirection === 'desc') return <ArrowDown className="h-4 w-4" />;
        return <ArrowUpDown className="h-4 w-4" />;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Members" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold tracking-tight">Church Members</h1>
                    <p className="text-muted-foreground">Manage and view all church members</p>
                </div>

                {/* Data Table Card */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        {/* Search and Controls */}
                        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div className="relative max-w-sm">
                                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Search members..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-muted-foreground">Show:</span>
                                <select
                                    value={itemsPerPage}
                                    onChange={(e) => {
                                        setItemsPerPage(Number(e.target.value));
                                        setCurrentPage(1);
                                    }}
                                    className="rounded-md border border-input bg-background px-3 py-1 text-sm"
                                >
                                    <option value={5}>5</option>
                                    <option value={10}>10</option>
                                    <option value={25}>25</option>
                                    <option value={50}>50</option>
                                </select>
                                <span className="text-sm text-muted-foreground">entries</span>
                            </div>
                        </div>

                        {/* Table */}
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b border-border">
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('name')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Name {getSortIcon('name')}
                                            </Button>
                                        </th>
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('email')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Email {getSortIcon('email')}
                                            </Button>
                                        </th>
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('phone')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Phone {getSortIcon('phone')}
                                            </Button>
                                        </th>
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('department')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Department {getSortIcon('department')}
                                            </Button>
                                        </th>
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('status')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Status {getSortIcon('status')}
                                            </Button>
                                        </th>
                                        <th className="pb-3 text-left">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleSort('joinDate')}
                                                className="h-auto p-0 font-semibold text-foreground hover:text-primary"
                                            >
                                                Join Date {getSortIcon('joinDate')}
                                            </Button>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedData.map((member) => (
                                        <tr key={member.id} className="border-b border-border/50 hover:bg-muted/50 transition-colors">
                                            <td className="py-4 font-medium">{member.name}</td>
                                            <td className="py-4 text-muted-foreground">{member.email}</td>
                                            <td className="py-4 text-muted-foreground">{member.phone}</td>
                                            <td className="py-4">{member.department}</td>
                                            <td className="py-4">
                                                <Badge className={statusColors[member.status]}>
                                                    {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                                                </Badge>
                                            </td>
                                            <td className="py-4 text-muted-foreground">
                                                {new Date(member.joinDate).toLocaleDateString()}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        <div className="mt-6 flex items-center justify-between">
                            <div className="text-sm text-muted-foreground">
                                Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredAndSortedData.length)} of {filteredAndSortedData.length} entries
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(1)}
                                    disabled={currentPage === 1}
                                >
                                    <ChevronsLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage === 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <span className="px-3 py-1 text-sm">
                                    Page {currentPage} of {totalPages}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(totalPages)}
                                    disabled={currentPage === totalPages}
                                >
                                    <ChevronsRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </AppLayout>
    );
}

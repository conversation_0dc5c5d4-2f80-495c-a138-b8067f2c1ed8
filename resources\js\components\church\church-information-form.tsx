import { LeadershipManager } from '@/components/church/leadership-manager';
import { ServiceTimesManager } from '@/components/church/service-times-manager';
import { SocialMediaLinksForm } from '@/components/church/social-media-links-form';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { type ExtendedChurchInformationFormProps } from '@/types/church-settings';
import { Settings } from 'lucide-react';

export function ChurchInformationForm({
    formData,
    onInputChange,
    onNestedChange,
    onServiceTimeChange,
    onAddServiceTime,
    onRemoveServiceTime,
    onLeadershipChange,
    onAddLeadership,
    onRemoveLeadership
}: ExtendedChurchInformationFormProps) {
    return (
        <Card className="p-6">
            <div className="mb-6 flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold">Church Information</h2>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                    <Label htmlFor="church_name">Church Name *</Label>
                    <Input
                        id="church_name"
                        value={formData.church_name}
                        onChange={(e) => onInputChange('church_name', e.target.value)}
                        placeholder="Enter church name"
                        required
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="church_email">Church Email</Label>
                    <Input
                        id="church_email"
                        type="email"
                        value={formData.church_email}
                        onChange={(e) => onInputChange('church_email', e.target.value)}
                        placeholder="<EMAIL>"
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="church_phone">Church Phone</Label>
                    <Input
                        id="church_phone"
                        value={formData.church_phone}
                        onChange={(e) => onInputChange('church_phone', e.target.value)}
                        placeholder="+****************"
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="church_website">Website URL</Label>
                    <Input
                        id="church_website"
                        type="url"
                        value={formData.church_website}
                        onChange={(e) => onInputChange('church_website', e.target.value)}
                        placeholder="https://www.yourchurch.com"
                    />
                </div>

                <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="church_address">Church Address</Label>
                    <Textarea
                        id="church_address"
                        value={formData.church_address}
                        onChange={(e) => onInputChange('church_address', e.target.value)}
                        placeholder="Enter complete church address"
                        rows={3}
                    />
                </div>

                <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="church_description">Church Description</Label>
                    <Textarea
                        id="church_description"
                        value={formData.church_description}
                        onChange={(e) => onInputChange('church_description', e.target.value)}
                        placeholder="Brief description of your church"
                        rows={3}
                    />
                </div>

                <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="mission_statement">Mission Statement</Label>
                    <Textarea
                        id="mission_statement"
                        value={formData.mission_statement}
                        onChange={(e) => onInputChange('mission_statement', e.target.value)}
                        placeholder="Your church's mission statement"
                        rows={4}
                    />
                </div>
            </div>

            <SocialMediaLinksForm
                formData={formData}
                onInputChange={onInputChange}
                onNestedChange={onNestedChange}
            />

            <ServiceTimesManager
                formData={formData}
                onInputChange={onInputChange}
                onNestedChange={onNestedChange}
                onServiceTimeChange={onServiceTimeChange}
                onAddServiceTime={onAddServiceTime}
                onRemoveServiceTime={onRemoveServiceTime}
            />

            <LeadershipManager
                formData={formData}
                onInputChange={onInputChange}
                onNestedChange={onNestedChange}
                onLeadershipChange={onLeadershipChange}
                onAddLeadership={onAddLeadership}
                onRemoveLeadership={onRemoveLeadership}
            />
        </Card>
    );
}

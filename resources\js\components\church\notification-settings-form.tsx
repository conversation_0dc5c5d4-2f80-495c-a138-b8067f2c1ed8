import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { type NotificationSettingsFormProps } from '@/types/church-settings';
import { Database } from 'lucide-react';

export function NotificationSettingsForm({ formData, onNestedChange }: NotificationSettingsFormProps) {
    return (
        <Card className="p-6">
            <div className="mb-6 flex items-center gap-2">
                <Database className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold">System Settings</h2>
            </div>

            <div className="space-y-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Notification Preferences</h3>
                    <div className="grid gap-4 md:grid-cols-3">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="email_notifications"
                                checked={formData.notification_settings?.email_enabled || false}
                                onChange={(e) => onNestedChange('notification_settings', 'email_enabled', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="email_notifications">Email Notifications</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="sms_notifications"
                                checked={formData.notification_settings?.sms_enabled || false}
                                onChange={(e) => onNestedChange('notification_settings', 'sms_enabled', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="sms_notifications">SMS Notifications</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="push_notifications"
                                checked={formData.notification_settings?.push_enabled || false}
                                onChange={(e) => onNestedChange('notification_settings', 'push_enabled', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="push_notifications">Push Notifications</Label>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
}

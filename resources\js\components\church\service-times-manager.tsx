import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { type ServiceTimesManagerProps } from '@/types/church-settings';

export function ServiceTimesManager({ 
    formData, 
    onServiceTimeChange, 
    onAddServiceTime, 
    onRemoveServiceTime 
}: ServiceTimesManagerProps) {
    return (
        <>
            <Separator className="my-6" />

            {/* Service Times */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Service Times</h3>
                    <Button type="button" variant="outline" onClick={onAddServiceTime}>
                        Add Service
                    </Button>
                </div>

                {formData.service_times?.map((service, index) => (
                    <div key={index} className="grid items-end gap-4 md:grid-cols-4">
                        <div className="space-y-2">
                            <Label htmlFor={`service_day_${index}`}>Day</Label>
                            <Input
                                id={`service_day_${index}`}
                                value={service.day}
                                onChange={(e) => onServiceTimeChange(index, 'day', e.target.value)}
                                placeholder="Sunday"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor={`service_time_${index}`}>Time</Label>
                            <Input
                                id={`service_time_${index}`}
                                value={service.time}
                                onChange={(e) => onServiceTimeChange(index, 'time', e.target.value)}
                                placeholder="9:00 AM"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor={`service_type_${index}`}>Service Type</Label>
                            <Input
                                id={`service_type_${index}`}
                                value={service.service_type}
                                onChange={(e) => onServiceTimeChange(index, 'service_type', e.target.value)}
                                placeholder="Main Service"
                            />
                        </div>

                        <div>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => onRemoveServiceTime(index)}
                                disabled={formData.service_times?.length === 1}
                            >
                                Remove
                            </Button>
                        </div>
                    </div>
                ))}
            </div>
        </>
    );
}

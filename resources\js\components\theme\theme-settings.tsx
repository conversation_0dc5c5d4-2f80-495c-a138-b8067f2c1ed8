/**
 * Velonic Theme Settings Component
 * Provides a comprehensive interface for customizing the theme
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useTheme } from '@/hooks/use-theme';
import { BRAND_COLOR_VARIANTS, VELONIC_COLORS } from '@/types/theme';

interface ThemeSettingsProps {
    className?: string;
}

export function ThemeSettings({ className }: ThemeSettingsProps) {
    const { config, toggleTheme, setSidebarTheme, setTopbarTheme, setPrimaryColor } = useTheme();

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle level={2}>Theme Settings</CardTitle>
                <CardDescription>Customize your Velonic theme appearance and colors</CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
                {/* Theme Mode */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Theme Mode</h4>
                    <div className="flex gap-2">
                        <Button
                            variant={config.mode === 'light' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => config.mode !== 'light' && toggleTheme()}
                        >
                            Light
                        </Button>
                        <Button
                            variant={config.mode === 'dark' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => config.mode !== 'dark' && toggleTheme()}
                        >
                            Dark
                        </Button>
                    </div>
                </div>

                <Separator />

                {/* Primary Color */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Primary Color</h4>
                    <div className="grid grid-cols-5 gap-2">
                        {BRAND_COLOR_VARIANTS.map((color) => (
                            <button
                                key={color}
                                onClick={() => setPrimaryColor(color)}
                                className={`relative h-8 w-full rounded-md border-2 transition-all hover:scale-105 focus:ring-2 focus:ring-ring focus:outline-none ${
                                    config.primaryColor === color ? 'border-ring ring-2 ring-ring/50' : 'border-border'
                                } `}
                                style={{ backgroundColor: VELONIC_COLORS[color] }}
                                title={color.charAt(0).toUpperCase() + color.slice(1)}
                            >
                                {config.primaryColor === color && (
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <svg className="h-4 w-4 text-white drop-shadow" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                )}
                            </button>
                        ))}
                    </div>
                    <p className="text-xs text-muted-foreground">
                        Current: <span className="font-medium text-foreground capitalize">{config.primaryColor}</span>
                    </p>
                </div>

                <Separator />

                {/* Sidebar Theme */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Sidebar Theme</h4>
                    <div className="flex gap-2">
                        <Button variant={config.sidebar === 'light' ? 'default' : 'outline'} size="sm" onClick={() => setSidebarTheme('light')}>
                            Light Sidebar
                        </Button>
                        <Button variant={config.sidebar === 'dark' ? 'default' : 'outline'} size="sm" onClick={() => setSidebarTheme('dark')}>
                            Dark Sidebar
                        </Button>
                    </div>
                </div>

                <Separator />

                {/* Topbar Theme */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Topbar Theme</h4>
                    <div className="flex gap-2">
                        <Button variant={config.topbar === 'light' ? 'default' : 'outline'} size="sm" onClick={() => setTopbarTheme('light')}>
                            Light Topbar
                        </Button>
                        <Button variant={config.topbar === 'dark' ? 'default' : 'outline'} size="sm" onClick={() => setTopbarTheme('dark')}>
                            Dark Topbar
                        </Button>
                    </div>
                </div>

                <Separator />

                {/* Current Configuration */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Current Configuration</h4>
                    <div className="space-y-2">
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Mode:</span>
                            <Badge variant="outline" className="capitalize">
                                {config.mode}
                            </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Primary Color:</span>
                            <Badge color={config.primaryColor} className="capitalize">
                                {config.primaryColor}
                            </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Sidebar:</span>
                            <Badge variant="outline" className="capitalize">
                                {config.sidebar}
                            </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">Topbar:</span>
                            <Badge variant="outline" className="capitalize">
                                {config.topbar}
                            </Badge>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

// Quick theme switcher button component
export function ThemeToggle({ className, showLabel = false }: { className?: string; showLabel?: boolean }) {
    const { config, toggleTheme } = useTheme();

    return (
        <Button variant="ghost" size="sm" onClick={toggleTheme} className={className}>
            {config.mode === 'light' ? (
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                    />
                </svg>
            ) : (
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                </svg>
            )}
            {showLabel && <span className="ml-2">{config.mode === 'light' ? 'Dark' : 'Light'}</span>}
        </Button>
    );
}

// Color palette showcase component
export function ColorPalette({ className }: { className?: string }) {
    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle level={3}>Velonic Color Palette</CardTitle>
                <CardDescription>Complete color system with brand and semantic colors</CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
                {/* Brand Colors */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Brand Colors</h4>
                    <div className="grid grid-cols-5 gap-3">
                        {BRAND_COLOR_VARIANTS.map((color) => (
                            <div key={color} className="space-y-2 text-center">
                                <div className="h-12 w-full rounded-md border" style={{ backgroundColor: VELONIC_COLORS[color] }} />
                                <div className="space-y-1">
                                    <p className="text-xs font-medium capitalize">{color}</p>
                                    <p className="font-mono text-xs text-muted-foreground">{VELONIC_COLORS[color]}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <Separator />

                {/* Component Examples */}
                <div className="space-y-3">
                    <h4 className="text-sm font-medium">Component Examples</h4>
                    <div className="space-y-4">
                        {/* Buttons */}
                        <div className="space-y-2">
                            <p className="text-xs text-muted-foreground">Buttons</p>
                            <div className="flex flex-wrap gap-2">
                                <Button color="blue" size="sm">
                                    Blue
                                </Button>
                                <Button color="indigo" size="sm">
                                    Indigo
                                </Button>
                                <Button color="purple" size="sm">
                                    Purple
                                </Button>
                                <Button color="pink" size="sm">
                                    Pink
                                </Button>
                                <Button color="green" size="sm">
                                    Green
                                </Button>
                            </div>
                        </div>

                        {/* Soft Buttons */}
                        <div className="space-y-2">
                            <p className="text-xs text-muted-foreground">Soft Buttons</p>
                            <div className="flex flex-wrap gap-2">
                                <Button variant="soft" color="cyan" size="sm">
                                    Soft Cyan
                                </Button>
                                <Button variant="soft" color="orange" size="sm">
                                    Soft Orange
                                </Button>
                                <Button variant="soft" color="yellow" size="sm">
                                    Soft Yellow
                                </Button>
                                <Button variant="soft" color="teal" size="sm">
                                    Soft Teal
                                </Button>
                            </div>
                        </div>

                        {/* Badges */}
                        <div className="space-y-2">
                            <p className="text-xs text-muted-foreground">Badges</p>
                            <div className="flex flex-wrap gap-2">
                                <Badge color="blue">Blue</Badge>
                                <Badge color="indigo">Indigo</Badge>
                                <Badge color="purple">Purple</Badge>
                                <Badge variant="soft" color="pink">
                                    Soft Pink
                                </Badge>
                                <Badge variant="outline" color="green">
                                    Outline Green
                                </Badge>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { type ContentManagementPageProps, type TabConfig } from '@/types/content-management';
import { Head } from '@inertiajs/react';
import { BookOpen, FileVideo, Music, Plus, Settings, Sparkles } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Content Management',
        href: '/content-management',
    },
];

const tabConfigs: TabConfig[] = [
    {
        key: 'overview',
        label: 'Overview',
        icon: Settings,
    },
    {
        key: 'videos',
        label: 'Videos',
        icon: FileVideo,
    },
    {
        key: 'hymns',
        label: 'Hymns',
        icon: Music,
    },
    {
        key: 'devotionals',
        label: 'Devotionals',
        icon: BookOpen,
    },
    {
        key: 'categories',
        label: 'Categories',
        icon: Sparkles,
    },
];

export default function ContentManagementPage({ categories, videos, hymns, devotionals, stats }: ContentManagementPageProps) {
    const [activeTab, setActiveTab] = useState('overview');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Content Management" />

            <div className="flex-1 space-y-6 p-6">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Content Management</h1>
                        <p className="text-muted-foreground">
                            Manage multimedia content for your church mobile app including videos, hymns, and devotionals.
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Button variant="outline" size="sm">
                            <Settings className="mr-2 h-4 w-4" />
                            Settings
                        </Button>
                        <Button size="sm">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Content
                        </Button>
                    </div>
                </div>

                {/* Main Content */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-5">
                        {tabConfigs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <TabsTrigger key={tab.key} value={tab.key} className="flex items-center gap-2">
                                    <Icon className="h-4 w-4" />
                                    <span className="hidden sm:inline">{tab.label}</span>
                                </TabsTrigger>
                            );
                        })}
                    </TabsList>

                    {/* Overview Tab */}
                    <TabsContent value="overview" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                            {/* Video Stats */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Videos</CardTitle>
                                    <FileVideo className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.videos.total}</div>
                                    <p className="text-xs text-muted-foreground">
                                        {stats.videos.published} published, {stats.videos.draft} drafts
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Hymn Stats */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Hymns</CardTitle>
                                    <Music className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.hymns.total}</div>
                                    <p className="text-xs text-muted-foreground">
                                        {stats.hymns.published} published, {stats.hymns.draft} drafts
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Devotional Stats */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Devotionals</CardTitle>
                                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.devotionals.total}</div>
                                    <p className="text-xs text-muted-foreground">
                                        {stats.devotionals.published} published, {stats.devotionals.scheduled} scheduled
                                    </p>
                                </CardContent>
                            </Card>

                            {/* Category Stats */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Categories</CardTitle>
                                    <Sparkles className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.categories.total}</div>
                                    <p className="text-xs text-muted-foreground">{stats.categories.active} active</p>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Recent Content */}
                        <div className="grid gap-6 lg:grid-cols-3">
                            {/* Recent Videos */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <FileVideo className="h-5 w-5" />
                                        Recent Videos
                                    </CardTitle>
                                    <CardDescription>Latest video content</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {videos.slice(0, 5).map((video) => (
                                        <div key={video.id} className="flex items-start space-x-3">
                                            <div className="min-w-0 flex-1">
                                                <p className="truncate text-sm font-medium">{video.title}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {video.speaker && `by ${video.speaker} • `}
                                                    {video.type}
                                                </p>
                                            </div>
                                            <div className="text-xs text-muted-foreground">{new Date(video.created_at).toLocaleDateString()}</div>
                                        </div>
                                    ))}
                                    {videos.length === 0 && <p className="text-sm text-muted-foreground">No videos yet</p>}
                                </CardContent>
                            </Card>

                            {/* Recent Hymns */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Music className="h-5 w-5" />
                                        Recent Hymns
                                    </CardTitle>
                                    <CardDescription>Latest hymn additions</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {hymns.slice(0, 5).map((hymn) => (
                                        <div key={hymn.id} className="flex items-start space-x-3">
                                            <div className="min-w-0 flex-1">
                                                <p className="truncate text-sm font-medium">{hymn.title}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {hymn.author && `by ${hymn.author} • `}
                                                    {hymn.type}
                                                </p>
                                            </div>
                                            <div className="text-xs text-muted-foreground">{new Date(hymn.created_at).toLocaleDateString()}</div>
                                        </div>
                                    ))}
                                    {hymns.length === 0 && <p className="text-sm text-muted-foreground">No hymns yet</p>}
                                </CardContent>
                            </Card>

                            {/* Recent Devotionals */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <BookOpen className="h-5 w-5" />
                                        Recent Devotionals
                                    </CardTitle>
                                    <CardDescription>Latest devotional content</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {devotionals.slice(0, 5).map((devotional) => (
                                        <div key={devotional.id} className="flex items-start space-x-3">
                                            <div className="min-w-0 flex-1">
                                                <p className="truncate text-sm font-medium">{devotional.title}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {devotional.author && `by ${devotional.author} • `}
                                                    {devotional.devotional_date && new Date(devotional.devotional_date).toLocaleDateString()}
                                                </p>
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                                {new Date(devotional.created_at).toLocaleDateString()}
                                            </div>
                                        </div>
                                    ))}
                                    {devotionals.length === 0 && <p className="text-sm text-muted-foreground">No devotionals yet</p>}
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Videos Tab */}
                    <TabsContent value="videos" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Video Management</CardTitle>
                                <CardDescription>Upload and manage video sermons, testimonies, and church programs</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground">Video management component will be implemented here.</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Hymns Tab */}
                    <TabsContent value="hymns" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Hymn Management</CardTitle>
                                <CardDescription>Add and organize hymn lyrics, chord charts, and sheet music</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground">Hymn management component will be implemented here.</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Devotionals Tab */}
                    <TabsContent value="devotionals" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Devotional Management</CardTitle>
                                <CardDescription>Create and schedule daily devotionals and inspirational content</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground">Devotional management component will be implemented here.</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Categories Tab */}
                    <TabsContent value="categories" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Category Management</CardTitle>
                                <CardDescription>Organize content with categories and tags</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground">Category management component will be implemented here.</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hymns', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();
            $table->string('title');
            $table->string('slug');
            $table->string('author')->nullable();
            $table->string('composer')->nullable();
            $table->year('year_written')->nullable();
            $table->text('lyrics'); // Full hymn lyrics
            $table->json('verse_structure')->nullable(); // Verse, chorus, bridge organization
            $table->enum('type', ['traditional', 'contemporary', 'seasonal', 'special'])->default('traditional');

            // Musical information
            $table->string('key_signature')->nullable(); // Musical key (C, G, D, etc.)
            $table->string('time_signature')->default('4/4');
            $table->integer('tempo_bpm')->nullable();
            $table->text('chord_progression')->nullable(); // Basic chord progression

            // File attachments
            $table->string('chord_chart_path')->nullable(); // Path to chord chart PDF/image
            $table->string('sheet_music_path')->nullable(); // Path to sheet music PDF
            $table->string('audio_path')->nullable(); // Path to audio file (optional)

            // Content organization
            $table->foreignId('category_id')->nullable()->constrained('content_categories')->onDelete('set null');
            $table->json('themes')->nullable(); // Array of themes/topics
            $table->json('scripture_references')->nullable(); // Related scripture

            // Publishing and visibility
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Usage tracking
            $table->integer('usage_count')->default(0); // How often it's been used in services
            $table->timestamp('last_used_at')->nullable();

            $table->timestamps();

            // Composite unique constraint for slug + tenant_id
            $table->unique(['slug', 'tenant_id']);

            // Indexes for performance
            $table->index('tenant_id');
            $table->index(['status', 'published_at']);
            $table->index(['type', 'status']);
            $table->index(['category_id', 'status']);
            $table->index('is_featured');
            $table->fullText(['title', 'lyrics', 'author']); // Full-text search
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hymns');
    }
};

<?php

namespace Database\Seeders;

use App\Models\ChurchSetting;
use Illuminate\Database\Seeder;

class ChurchSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ChurchSetting::updateOrCreate(
            ['id' => 1],
            [
                'church_name' => 'Flockin Church',
                'church_address' => '123 Church Street, City, State 12345',
                'church_phone' => '+****************',
                'church_email' => '<EMAIL>',
                'church_website' => 'https://www.flockingchurch.com',
                'social_media_links' => [
                    'facebook' => 'https://facebook.com/flockingchurch',
                    'twitter' => 'https://twitter.com/flockingchurch',
                    'instagram' => 'https://instagram.com/flockingchurch',
                    'youtube' => 'https://youtube.com/flockingchurch',
                ],
                'church_description' => 'A welcoming community church dedicated to serving <PERSON> and our neighbors with love, compassion, and faith.',
                'mission_statement' => 'To know Christ and make Him known through worship, fellowship, discipleship, and service to our community and the world.',
                'service_times' => [
                    [
                        'day' => 'Sunday',
                        'time' => '9:00 AM',
                        'service_type' => 'Main Service',
                    ],
                    [
                        'day' => 'Sunday',
                        'time' => '11:00 AM',
                        'service_type' => 'Contemporary Service',
                    ],
                    [
                        'day' => 'Wednesday',
                        'time' => '7:00 PM',
                        'service_type' => 'Bible Study',
                    ],
                ],
                'leadership_info' => [
                    [
                        'name' => 'Pastor John Smith',
                        'position' => 'Senior Pastor',
                        'bio' => 'Pastor John has been serving our church community for over 15 years, bringing wisdom, compassion, and spiritual guidance to all.',
                    ],
                    [
                        'name' => 'Sarah Johnson',
                        'position' => 'Associate Pastor',
                        'bio' => 'Sarah leads our youth ministry and community outreach programs with enthusiasm and dedication.',
                    ],
                ],
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'geofence_radius' => 150,
                'attendance_preferences' => [
                    'auto_checkin' => true,
                    'notification_enabled' => true,
                    'late_threshold' => 30,
                ],
                'manual_checkin_settings' => [
                    'enabled' => true,
                    'admin_only' => false,
                    'time_window' => 120,
                ],
                'notification_settings' => [
                    'email_enabled' => true,
                    'sms_enabled' => false,
                    'push_enabled' => true,
                ],
                'attendance_report_configs' => [
                    'weekly_reports' => true,
                    'monthly_reports' => true,
                    'auto_email' => true,
                ],
                'user_roles_permissions' => [
                    'admin' => ['all'],
                    'pastor' => ['view_all', 'manage_members', 'manage_attendance'],
                    'member' => ['view_own', 'checkin'],
                ],
                'notification_preferences' => [
                    'attendance_alerts' => true,
                    'event_reminders' => true,
                    'system_updates' => false,
                ],
                'backup_settings' => [
                    'auto_backup' => true,
                    'backup_frequency' => 'daily',
                    'retention_days' => 30,
                ],
                'privacy_security_configs' => [
                    'data_retention_days' => 365,
                    'require_2fa' => false,
                    'audit_logs' => true,
                ],
                'mobile_app_config' => [
                    'app_name' => 'Flockin Church',
                    'theme_color' => '#34B0E0',
                    'notification_enabled' => true,
                ],
                'third_party_integrations' => [
                    'google_calendar' => false,
                    'mailchimp' => false,
                    'zoom' => false,
                ],
                'api_keys' => [
                    // Add API keys here if needed
                ],
            ]
        );
    }
}

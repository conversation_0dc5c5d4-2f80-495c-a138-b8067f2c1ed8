import { router } from '@inertiajs/react';

export interface ExportOptions {
    reportType: string;
    format: 'pdf' | 'excel';
    filters?: Record<string, any>;
    filename?: string;
}

export class ReportExporter {
    /**
     * Export a report with the specified options
     */
    static async exportReport(options: ExportOptions): Promise<void> {
        try {
            const response = await fetch('/reports/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    report_type: options.reportType,
                    format: options.format,
                    filters: options.filters || {},
                    filename: options.filename,
                }),
            });

            if (!response.ok) {
                throw new Error('Export request failed');
            }

            const result = await response.json();

            if (result.success && result.download_url) {
                // Create a temporary link to download the file
                const link = document.createElement('a');
                link.href = result.download_url;
                link.download = result.filename || `report.${options.format}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                throw new Error(result.message || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            throw error;
        }
    }

    /**
     * Export weekly attendance report
     */
    static async exportWeeklyAttendance(format: 'pdf' | 'excel', filters?: Record<string, any>): Promise<void> {
        return this.exportReport({
            reportType: 'weekly-attendance',
            format,
            filters,
            filename: `weekly-attendance-${new Date().toISOString().split('T')[0]}.${format}`,
        });
    }

    /**
     * Export monthly trends report
     */
    static async exportMonthlyTrends(format: 'pdf' | 'excel', filters?: Record<string, any>): Promise<void> {
        return this.exportReport({
            reportType: 'monthly-trends',
            format,
            filters,
            filename: `monthly-trends-${new Date().toISOString().split('T')[0]}.${format}`,
        });
    }

    /**
     * Export service attendance report
     */
    static async exportServiceAttendance(format: 'pdf' | 'excel', filters?: Record<string, any>): Promise<void> {
        return this.exportReport({
            reportType: 'service-attendance',
            format,
            filters,
            filename: `service-attendance-${new Date().toISOString().split('T')[0]}.${format}`,
        });
    }

    /**
     * Export member attendance report
     */
    static async exportMemberAttendance(format: 'pdf' | 'excel', filters?: Record<string, any>): Promise<void> {
        return this.exportReport({
            reportType: 'member-attendance',
            format,
            filters,
            filename: `member-attendance-${new Date().toISOString().split('T')[0]}.${format}`,
        });
    }

    /**
     * Export attendance comparison report
     */
    static async exportAttendanceComparison(format: 'pdf' | 'excel', filters?: Record<string, any>): Promise<void> {
        return this.exportReport({
            reportType: 'attendance-comparison',
            format,
            filters,
            filename: `attendance-comparison-${new Date().toISOString().split('T')[0]}.${format}`,
        });
    }
}

/**
 * Convert data to CSV format
 */
export function convertToCSV(data: any[], headers?: string[]): string {
    if (!data.length) return '';

    const csvHeaders = headers || Object.keys(data[0]);
    const csvRows = data.map(row => 
        csvHeaders.map(header => {
            const value = row[header];
            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
        }).join(',')
    );

    return [csvHeaders.join(','), ...csvRows].join('\n');
}

/**
 * Download data as CSV file
 */
export function downloadCSV(data: any[], filename: string, headers?: string[]): void {
    const csv = convertToCSV(data, headers);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * Format data for export
 */
export function formatDataForExport(data: any[], type: string): any[] {
    switch (type) {
        case 'weekly-attendance':
            return data.map(item => ({
                'Date': new Date(item.date).toLocaleDateString(),
                'Service Type': item.service_type,
                'Attendance': item.attendance,
                'Target': item.target,
                'Achievement %': item.percentage,
            }));

        case 'monthly-trends':
            return data.map(item => ({
                'Month': item.month,
                'Attendance': item.attendance,
                'Target': item.target,
                'Growth Rate %': item.growth,
            }));

        case 'service-attendance':
            return data.map(item => ({
                'Service Type': item.service_type,
                'Average Attendance': item.average_attendance,
                'Total Services': item.total_services,
                'Highest': item.highest,
                'Lowest': item.lowest,
                'Trend': item.trend,
            }));

        case 'member-attendance':
            return data.map(item => ({
                'Member Name': item.name,
                'Total Services': item.total_services,
                'Attended': item.attended,
                'Attendance Rate %': item.attendance_rate,
                'Current Streak': item.streak,
                'Last Attended': new Date(item.last_attended).toLocaleDateString(),
            }));

        default:
            return data;
    }
}

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type WeeklyAttendanceData } from '@/types/reports';
import { router } from '@inertiajs/react';
import { Calendar, Search, TrendingDown, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface WeeklyAttendanceReportProps {
    initialData?: WeeklyAttendanceData[];
}

export function WeeklyAttendanceReport({ initialData = [] }: WeeklyAttendanceReportProps) {
    const [data, setData] = useState<WeeklyAttendanceData[]>(initialData);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState({
        start_date: '',
        end_date: '',
        service_type: 'all',
    });

    const fetchWeeklyData = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (filters.start_date) params.append('start_date', filters.start_date);
            if (filters.end_date) params.append('end_date', filters.end_date);
            if (filters.service_type !== 'all') params.append('service_type', filters.service_type);

            const response = await fetch(`/reports/weekly-attendance?${params.toString()}`);
            const result = await response.json();
            setData(result.data || []);
        } catch (error) {
            console.error('Error fetching weekly attendance data:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchWeeklyData();
    }, []);

    const handleFilterChange = (key: string, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const handleSearch = () => {
        fetchWeeklyData();
    };

    const totalAttendance = data.reduce((sum, item) => sum + item.attendance, 0);
    const averageAttendance = data.length > 0 ? Math.round(totalAttendance / data.length) : 0;
    const targetMet = data.filter(item => item.attendance >= item.target).length;
    const targetPercentage = data.length > 0 ? Math.round((targetMet / data.length) * 100) : 0;

    return (
        <div className="space-y-6">
            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Weekly Attendance Filters
                    </CardTitle>
                    <CardDescription>Filter weekly attendance data by date range and service type</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-4">
                        <div className="space-y-2">
                            <Label htmlFor="start_date">Start Date</Label>
                            <Input
                                id="start_date"
                                type="date"
                                value={filters.start_date}
                                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="end_date">End Date</Label>
                            <Input
                                id="end_date"
                                type="date"
                                value={filters.end_date}
                                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="service_type">Service Type</Label>
                            <Select value={filters.service_type} onValueChange={(value) => handleFilterChange('service_type', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select service type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Services</SelectItem>
                                    <SelectItem value="sunday_morning">Sunday Morning</SelectItem>
                                    <SelectItem value="wednesday_evening">Wednesday Evening</SelectItem>
                                    <SelectItem value="youth">Youth Service</SelectItem>
                                    <SelectItem value="special">Special Events</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-end">
                            <Button onClick={handleSearch} disabled={loading} className="w-full">
                                <Search className="mr-2 h-4 w-4" />
                                {loading ? 'Loading...' : 'Search'}
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Attendance</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalAttendance.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">Across all services</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Average Attendance</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{averageAttendance}</div>
                        <p className="text-xs text-muted-foreground">Per service</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
                        {targetPercentage >= 80 ? (
                            <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                            <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{targetPercentage}%</div>
                        <p className="text-xs text-muted-foreground">{targetMet} of {data.length} services</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Services Tracked</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{data.length}</div>
                        <p className="text-xs text-muted-foreground">In selected period</p>
                    </CardContent>
                </Card>
            </div>

            {/* Chart */}
            <Card>
                <CardHeader>
                    <CardTitle>Weekly Attendance Chart</CardTitle>
                    <CardDescription>Attendance vs target for each service</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={data}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis 
                                        dataKey="date" 
                                        stroke="#6b7280"
                                        tick={{ fontSize: 12 }}
                                    />
                                    <YAxis stroke="#6b7280" />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: 'white', 
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '8px'
                                        }} 
                                    />
                                    <Bar dataKey="attendance" fill="#3b82f6" name="Attendance" />
                                    <Bar dataKey="target" fill="#e5e7eb" name="Target" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    ) : (
                        <div className="flex h-80 items-center justify-center text-muted-foreground">
                            {loading ? 'Loading attendance data...' : 'No attendance data available for the selected period.'}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Data Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Detailed Attendance Data</CardTitle>
                    <CardDescription>Service-by-service attendance breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Date</th>
                                        <th className="text-left p-2">Service Type</th>
                                        <th className="text-right p-2">Attendance</th>
                                        <th className="text-right p-2">Target</th>
                                        <th className="text-right p-2">Achievement</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {data.map((item, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="p-2">{new Date(item.date).toLocaleDateString()}</td>
                                            <td className="p-2">{item.service_type}</td>
                                            <td className="text-right p-2">{item.attendance}</td>
                                            <td className="text-right p-2">{item.target}</td>
                                            <td className="text-right p-2">
                                                <span className={`px-2 py-1 rounded text-xs ${
                                                    item.percentage >= 100 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : item.percentage >= 80 
                                                        ? 'bg-yellow-100 text-yellow-800'
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {item.percentage}%
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground py-8">
                            No data available for the selected filters.
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class HymnRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $hymnId = $this->route('hymn')?->id;

        return [
            'title' => ['required', 'string', 'max:255'],
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('hymns', 'slug')->ignore($hymnId),
            ],
            'author' => ['nullable', 'string', 'max:255'],
            'composer' => ['nullable', 'string', 'max:255'],
            'year_written' => ['nullable', 'integer', 'min:1', 'max:' . date('Y')],
            'lyrics' => ['required', 'string'],
            'verse_structure' => ['nullable', 'array'],
            'type' => ['required', 'in:traditional,contemporary,seasonal,special'],

            // Musical information
            'key_signature' => ['nullable', 'string', 'max:10'],
            'time_signature' => ['nullable', 'string', 'max:10'],
            'tempo_bpm' => ['nullable', 'integer', 'min:40', 'max:200'],
            'chord_progression' => ['nullable', 'string', 'max:1000'],

            // File attachments
            'chord_chart' => [
                'nullable',
                'file',
                'mimes:pdf,jpg,jpeg,png,gif',
                'max:10240', // 10MB
            ],
            'sheet_music' => [
                'nullable',
                'file',
                'mimes:pdf,jpg,jpeg,png,gif',
                'max:10240', // 10MB
            ],
            'audio_file' => [
                'nullable',
                'file',
                'mimes:mp3,wav,ogg,m4a',
                'max:51200', // 50MB
            ],

            // Content organization
            'category_id' => ['nullable', 'exists:content_categories,id'],
            'themes' => ['nullable', 'array'],
            'themes.*' => ['string', 'max:100'],
            'scripture_references' => ['nullable', 'array'],
            'scripture_references.*' => ['string', 'max:100'],

            // Publishing
            'status' => ['required', 'in:draft,published,archived'],
            'is_featured' => ['boolean'],
            'published_at' => ['nullable', 'date', 'after_or_equal:today'],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Hymn title is required.',
            'title.max' => 'Hymn title cannot exceed 255 characters.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'slug.regex' => 'Slug can only contain lowercase letters, numbers, and hyphens.',
            'author.max' => 'Author name cannot exceed 255 characters.',
            'composer.max' => 'Composer name cannot exceed 255 characters.',
            'year_written.min' => 'Year written must be a valid year.',
            'year_written.max' => 'Year written cannot be in the future.',
            'lyrics.required' => 'Hymn lyrics are required.',
            'type.required' => 'Hymn type is required.',
            'type.in' => 'Invalid hymn type selected.',

            // Musical validation messages
            'key_signature.max' => 'Key signature cannot exceed 10 characters.',
            'time_signature.max' => 'Time signature cannot exceed 10 characters.',
            'tempo_bpm.min' => 'Tempo must be at least 40 BPM.',
            'tempo_bpm.max' => 'Tempo cannot exceed 200 BPM.',
            'chord_progression.max' => 'Chord progression cannot exceed 1000 characters.',

            // File validation messages
            'chord_chart.file' => 'Please upload a valid chord chart file.',
            'chord_chart.mimes' => 'Chord chart must be a PDF or image file (JPG, JPEG, PNG, GIF).',
            'chord_chart.max' => 'Chord chart file size cannot exceed 10MB.',
            'sheet_music.file' => 'Please upload a valid sheet music file.',
            'sheet_music.mimes' => 'Sheet music must be a PDF or image file (JPG, JPEG, PNG, GIF).',
            'sheet_music.max' => 'Sheet music file size cannot exceed 10MB.',
            'audio_file.file' => 'Please upload a valid audio file.',
            'audio_file.mimes' => 'Audio file must be in one of these formats: MP3, WAV, OGG, M4A.',
            'audio_file.max' => 'Audio file size cannot exceed 50MB.',

            // Organization messages
            'category_id.exists' => 'Selected category does not exist.',
            'themes.*.max' => 'Each theme cannot exceed 100 characters.',
            'scripture_references.*.max' => 'Each scripture reference cannot exceed 100 characters.',

            // Publishing messages
            'status.required' => 'Hymn status is required.',
            'status.in' => 'Invalid hymn status selected.',
            'published_at.after_or_equal' => 'Published date cannot be in the past.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'chord_chart' => 'chord chart',
            'sheet_music' => 'sheet music',
            'audio_file' => 'audio file',
            'is_featured' => 'featured status',
            'published_at' => 'publish date',
            'category_id' => 'category',
            'tempo_bpm' => 'tempo (BPM)',
        ];
    }
}

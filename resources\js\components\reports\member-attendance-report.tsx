import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type MemberAttendanceData } from '@/types/reports';
import { Award, Search, TrendingDown, TrendingUp, User, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface MemberAttendanceReportProps {
    initialData?: MemberAttendanceData[];
}

export function MemberAttendanceReport({ initialData = [] }: MemberAttendanceReportProps) {
    const [data, setData] = useState<MemberAttendanceData[]>(initialData);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState({
        member_id: '',
        period: 'month',
        search: '',
        sort_by: 'attendance_rate',
        sort_order: 'desc',
    });

    const fetchMemberData = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (filters.member_id) params.append('member_id', filters.member_id);
            params.append('period', filters.period);
            if (filters.search) params.append('search', filters.search);
            params.append('sort_by', filters.sort_by);
            params.append('sort_order', filters.sort_order);

            const response = await fetch(`/reports/member-attendance?${params.toString()}`);
            const result = await response.json();
            setData(result.data || []);
        } catch (error) {
            console.error('Error fetching member attendance data:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchMemberData();
    }, []);

    const handleFilterChange = (key: string, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const handleSearch = () => {
        fetchMemberData();
    };

    // Calculate summary statistics
    const totalMembers = data.length;
    const averageAttendanceRate = data.length > 0 
        ? data.reduce((sum, member) => sum + member.attendance_rate, 0) / data.length 
        : 0;
    const perfectAttendees = data.filter(member => member.attendance_rate === 100).length;
    const regularAttendees = data.filter(member => member.attendance_rate >= 80).length;

    // Prepare data for chart (top 10 members by attendance rate)
    const chartData = data
        .slice(0, 10)
        .map(member => ({
            name: member.name.split(' ').map(n => n[0]).join(''), // Initials for chart
            fullName: member.name,
            attendance_rate: member.attendance_rate,
        }));

    const getAttendanceColor = (rate: number) => {
        if (rate >= 90) return 'text-green-600 bg-green-100';
        if (rate >= 70) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    const getStreakColor = (streak: number) => {
        if (streak >= 10) return 'text-green-600';
        if (streak >= 5) return 'text-yellow-600';
        return 'text-gray-600';
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Member Attendance Tracking
                    </CardTitle>
                    <CardDescription>Track individual member attendance patterns and engagement</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-5">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Search Members</label>
                            <Input
                                placeholder="Search by name..."
                                value={filters.search}
                                onChange={(e) => handleFilterChange('search', e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Period</label>
                            <Select value={filters.period} onValueChange={(value) => handleFilterChange('period', value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="week">This Week</SelectItem>
                                    <SelectItem value="month">This Month</SelectItem>
                                    <SelectItem value="quarter">This Quarter</SelectItem>
                                    <SelectItem value="year">This Year</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Sort By</label>
                            <Select value={filters.sort_by} onValueChange={(value) => handleFilterChange('sort_by', value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="attendance_rate">Attendance Rate</SelectItem>
                                    <SelectItem value="streak">Current Streak</SelectItem>
                                    <SelectItem value="total_attended">Total Attended</SelectItem>
                                    <SelectItem value="name">Name</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Order</label>
                            <Select value={filters.sort_order} onValueChange={(value) => handleFilterChange('sort_order', value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="desc">Highest First</SelectItem>
                                    <SelectItem value="asc">Lowest First</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-end">
                            <Button onClick={handleSearch} disabled={loading} className="w-full">
                                <Search className="mr-2 h-4 w-4" />
                                {loading ? 'Loading...' : 'Search'}
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Members</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalMembers}</div>
                        <p className="text-xs text-muted-foreground">Tracked members</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Average Rate</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{averageAttendanceRate.toFixed(1)}%</div>
                        <p className="text-xs text-muted-foreground">Overall attendance</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Perfect Attendance</CardTitle>
                        <Award className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{perfectAttendees}</div>
                        <p className="text-xs text-muted-foreground">100% attendance</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Regular Attendees</CardTitle>
                        <Users className="h-4 w-4 text-blue-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-blue-600">{regularAttendees}</div>
                        <p className="text-xs text-muted-foreground">80%+ attendance</p>
                    </CardContent>
                </Card>
            </div>

            {/* Top Performers Chart */}
            <Card>
                <CardHeader>
                    <CardTitle>Top Performers</CardTitle>
                    <CardDescription>Members with highest attendance rates</CardDescription>
                </CardHeader>
                <CardContent>
                    {chartData.length > 0 ? (
                        <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis 
                                        dataKey="name" 
                                        stroke="#6b7280"
                                        tick={{ fontSize: 12 }}
                                    />
                                    <YAxis 
                                        stroke="#6b7280"
                                        domain={[0, 100]}
                                    />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: 'white', 
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '8px'
                                        }}
                                        formatter={(value: number, name: string, props: any) => [
                                            `${value.toFixed(1)}%`, 
                                            `${props.payload.fullName} - Attendance Rate`
                                        ]}
                                    />
                                    <Bar 
                                        dataKey="attendance_rate" 
                                        fill="#3b82f6"
                                        radius={[4, 4, 0, 0]}
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    ) : (
                        <div className="flex h-80 items-center justify-center text-muted-foreground">
                            {loading ? 'Loading member data...' : 'No member data available.'}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Member Details Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Member Attendance Details</CardTitle>
                    <CardDescription>Comprehensive attendance tracking for all members</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Member</th>
                                        <th className="text-right p-2">Attended</th>
                                        <th className="text-right p-2">Total Services</th>
                                        <th className="text-right p-2">Attendance Rate</th>
                                        <th className="text-right p-2">Current Streak</th>
                                        <th className="text-right p-2">Last Attended</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {data.map((member, index) => (
                                        <tr key={index} className="border-b hover:bg-gray-50">
                                            <td className="p-2">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                                                        {member.name.split(' ').map(n => n[0]).join('')}
                                                    </div>
                                                    <span className="font-medium">{member.name}</span>
                                                </div>
                                            </td>
                                            <td className="text-right p-2">{member.attended}</td>
                                            <td className="text-right p-2">{member.total_services}</td>
                                            <td className="text-right p-2">
                                                <span className={`px-2 py-1 rounded text-xs font-medium ${getAttendanceColor(member.attendance_rate)}`}>
                                                    {member.attendance_rate.toFixed(1)}%
                                                </span>
                                            </td>
                                            <td className="text-right p-2">
                                                <span className={`font-medium ${getStreakColor(member.streak)}`}>
                                                    {member.streak}
                                                </span>
                                            </td>
                                            <td className="text-right p-2 text-muted-foreground">
                                                {new Date(member.last_attended).toLocaleDateString()}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground py-8">
                            {loading ? 'Loading member attendance data...' : 'No member data available for the selected filters.'}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

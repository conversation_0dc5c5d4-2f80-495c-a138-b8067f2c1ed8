import { Alert } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { type LocationSettingsFormProps } from '@/types/church-settings';
import { AlertTriangle, MapPin } from 'lucide-react';

export function LocationSettingsForm({ 
    formData, 
    coordinateErrors, 
    onCoordinateChange, 
    onTestGeofence 
}: LocationSettingsFormProps) {
    return (
        <Card className="p-6">
            <div className="mb-6 flex items-center gap-2">
                <MapPin className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold">Geolocation & Geofence Settings</h2>
            </div>

            <div className="space-y-6">
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium">Church Location Coordinates</h3>
                        <Button type="button" variant="outline" onClick={onTestGeofence}>
                            Test Geofence
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div className="space-y-2">
                            <Label htmlFor="latitude" className="text-sm font-medium">
                                Latitude *
                            </Label>
                            <Input
                                id="latitude"
                                type="number"
                                step="0.000001"
                                min="-90"
                                max="90"
                                placeholder="e.g., 37.774929"
                                value={formData.latitude || ''}
                                onChange={(e) => onCoordinateChange('latitude', e.target.value)}
                                className={`font-mono ${coordinateErrors.latitude ? 'border-red-500' : ''}`}
                            />
                            {coordinateErrors.latitude ? (
                                <p className="text-xs text-red-600">{coordinateErrors.latitude}</p>
                            ) : (
                                <p className="text-xs text-muted-foreground">Latitude ranges from -90 to 90 degrees</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="longitude" className="text-sm font-medium">
                                Longitude *
                            </Label>
                            <Input
                                id="longitude"
                                type="number"
                                step="0.000001"
                                min="-180"
                                max="180"
                                placeholder="e.g., -122.419416"
                                value={formData.longitude || ''}
                                onChange={(e) => onCoordinateChange('longitude', e.target.value)}
                                className={`font-mono ${coordinateErrors.longitude ? 'border-red-500' : ''}`}
                            />
                            {coordinateErrors.longitude ? (
                                <p className="text-xs text-red-600">{coordinateErrors.longitude}</p>
                            ) : (
                                <p className="text-xs text-muted-foreground">Longitude ranges from -180 to 180 degrees</p>
                            )}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="geofence_radius" className="text-sm font-medium">
                            Geofence Radius (meters)
                        </Label>
                        <Input
                            id="geofence_radius"
                            type="number"
                            min="10"
                            max="10000"
                            step="10"
                            value={formData.geofence_radius || ''}
                            onChange={(e) => onCoordinateChange('geofence_radius', e.target.value)}
                            placeholder="150"
                            className={`max-w-xs ${coordinateErrors.geofence_radius ? 'border-red-500' : ''}`}
                        />
                        {coordinateErrors.geofence_radius ? (
                            <p className="text-xs text-red-600">{coordinateErrors.geofence_radius}</p>
                        ) : (
                            <p className="text-xs text-muted-foreground">
                                Distance in meters for automatic check-in detection (10-10,000m)
                            </p>
                        )}
                    </div>

                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <div className="space-y-2">
                            <p className="text-sm font-medium">How to get your church coordinates:</p>
                            <ol className="ml-4 list-decimal space-y-1 text-sm">
                                <li>Open Google Maps on your phone or computer</li>
                                <li>Search for your church address</li>
                                <li>Right-click (or tap and hold) on the exact location</li>
                                <li>Copy the coordinates that appear (format: 37.774929, -122.419416)</li>
                                <li>Enter the first number as Latitude, second as Longitude</li>
                            </ol>
                        </div>
                    </Alert>

                    {formData.latitude && formData.longitude && (
                        <div className="rounded-lg bg-muted p-4">
                            <h4 className="mb-2 text-sm font-medium">Current Location</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-muted-foreground">Latitude:</span>
                                    <span className="ml-2 font-mono">{Number(formData.latitude).toFixed(6)}</span>
                                </div>
                                <div>
                                    <span className="text-muted-foreground">Longitude:</span>
                                    <span className="ml-2 font-mono">{Number(formData.longitude).toFixed(6)}</span>
                                </div>
                            </div>
                            {formData.geofence_radius && (
                                <div className="mt-2 text-sm">
                                    <span className="text-muted-foreground">Geofence Radius:</span>
                                    <span className="ml-2">{formData.geofence_radius}m</span>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </Card>
    );
}

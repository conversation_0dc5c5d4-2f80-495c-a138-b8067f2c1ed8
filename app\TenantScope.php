<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        // Only apply tenant scope if we have a tenant context and bypass is not enabled
        if (app()->bound('tenant') && !app()->bound('tenant.bypass')) {
            $tenant = app('tenant');
            if ($tenant && $tenant->id) {
                $builder->where($model->getTable() . '.tenant_id', $tenant->id);
            }
        }
    }

    /**
     * Extend the query builder with the needed functions.
     */
    public function extend(Builder $builder): void
    {
        $builder->macro(
            'withoutTenantScope',
            fn(Builder $builder) =>
            $builder->withoutGlobalScope(TenantScope::class)
        );

        $builder->macro(
            'withAllTenants',
            fn(Builder $builder) =>
            $builder->withoutGlobalScope(TenantScope::class)
        );
    }
}

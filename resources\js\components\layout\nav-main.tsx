import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();
    return (
        <SidebarGroup className="px-0 py-0">
            <SidebarGroupLabel className="mb-1 px-4 py-3 text-xs font-semibold tracking-wider text-sidebar-foreground/60 uppercase">
                Main
            </SidebarGroupLabel>
            <SidebarMenu className="space-y-1 px-2">
                {items.map((item) => {
                    const isActive = page.url.startsWith(item.href);
                    return (
                        <SidebarMenuItem key={item.title}>
                            <SidebarMenuButton
                                asChild
                                isActive={isActive}
                                className={`data-[active=true]:text-menu-item-active-color h-10 rounded-lg px-3 text-sidebar-foreground transition-all duration-200 ease-in-out group-data-[collapsible=icon]:justify-center hover:bg-sidebar-accent/80 hover:text-sidebar-accent-foreground data-[active=true]:bg-menu-item-active-bg data-[active=true]:font-medium data-[active=true]:shadow-sm ${isActive ? 'text-menu-item-active-color bg-menu-item-active-bg font-medium shadow-sm' : ''} `}
                                tooltip={{ children: item.title }}
                            >
                                <Link href={item.href} prefetch className="flex w-full items-center gap-3">
                                    <>
                                        {item.icon && (
                                            <item.icon
                                                className={`size-5 shrink-0 transition-colors duration-200 ${isActive ? 'text-menu-item-active-color' : 'text-sidebar-foreground/70'} `}
                                            />
                                        )}
                                        <span className="truncate text-sm font-medium group-data-[collapsible=icon]:sr-only">{item.title}</span>
                                    </>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    );
                })}
            </SidebarMenu>
        </SidebarGroup>
    );
}

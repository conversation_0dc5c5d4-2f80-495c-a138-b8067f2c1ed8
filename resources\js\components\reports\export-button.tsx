import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { downloadCSV, formatDataForExport, ReportExporter } from '@/utils/export-utils';
import { ChevronDown, Download, FileSpreadsheet, FileText } from 'lucide-react';
import { useState } from 'react';

interface ExportButtonProps {
    reportType: string;
    data?: any[];
    filters?: Record<string, any>;
    disabled?: boolean;
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'sm' | 'default' | 'lg';
}

export function ExportButton({ 
    reportType, 
    data = [], 
    filters = {}, 
    disabled = false,
    variant = 'outline',
    size = 'default'
}: ExportButtonProps) {
    const [isExporting, setIsExporting] = useState(false);

    const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
        if (disabled || isExporting) return;

        setIsExporting(true);
        try {
            if (format === 'csv' && data.length > 0) {
                // Handle CSV export client-side
                const formattedData = formatDataForExport(data, reportType);
                const filename = `${reportType}-${new Date().toISOString().split('T')[0]}.csv`;
                downloadCSV(formattedData, filename);
            } else {
                // Handle PDF and Excel export server-side
                switch (reportType) {
                    case 'weekly-attendance':
                        await ReportExporter.exportWeeklyAttendance(format, filters);
                        break;
                    case 'monthly-trends':
                        await ReportExporter.exportMonthlyTrends(format, filters);
                        break;
                    case 'service-attendance':
                        await ReportExporter.exportServiceAttendance(format, filters);
                        break;
                    case 'member-attendance':
                        await ReportExporter.exportMemberAttendance(format, filters);
                        break;
                    case 'attendance-comparison':
                        await ReportExporter.exportAttendanceComparison(format, filters);
                        break;
                    default:
                        await ReportExporter.exportReport({
                            reportType,
                            format,
                            filters,
                        });
                }
            }
        } catch (error) {
            console.error('Export failed:', error);
            // You could show a toast notification here
        } finally {
            setIsExporting(false);
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button 
                    variant={variant} 
                    size={size}
                    disabled={disabled || isExporting}
                >
                    <Download className="mr-2 h-4 w-4" />
                    {isExporting ? 'Exporting...' : 'Export'}
                    <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleExport('pdf')}>
                    <FileText className="mr-2 h-4 w-4" />
                    Export as PDF
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('excel')}>
                    <FileSpreadsheet className="mr-2 h-4 w-4" />
                    Export as Excel
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                    <FileSpreadsheet className="mr-2 h-4 w-4" />
                    Export as CSV
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

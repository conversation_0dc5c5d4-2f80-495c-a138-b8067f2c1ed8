<?php

namespace App\Http\Controllers;

use App\Models\AttendanceRecord;
use App\Models\Service;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class ReportsController extends Controller
{
    /**
     * Display the main reports page.
     */
    public function index(): Response
    {
        // Get basic stats for the overview
        $stats = $this->getReportStats();

        return Inertia::render('reports', [
            'stats' => $stats,
        ]);
    }

    /**
     * Get weekly attendance summary.
     */
    public function weeklyAttendance(Request $request): JsonResponse
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfWeek());
        $endDate = $request->get('end_date', Carbon::now()->endOfWeek());

        // Mock data for now - will be replaced with actual database queries
        $weeklyData = [
            [
                'date' => '2025-01-05',
                'service_type' => 'Sunday Morning',
                'attendance' => 245,
                'target' => 250,
                'percentage' => 98,
            ],
            [
                'date' => '2025-01-08',
                'service_type' => 'Wednesday Evening',
                'attendance' => 89,
                'target' => 100,
                'percentage' => 89,
            ],
        ];

        return response()->json([
            'data' => $weeklyData,
            'period' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
        ]);
    }

    /**
     * Get monthly attendance trends.
     */
    public function monthlyTrends(Request $request): JsonResponse
    {
        $year = $request->get('year', Carbon::now()->year);

        // Mock data for monthly trends
        $monthlyData = [
            ['month' => 'Jan', 'attendance' => 980, 'target' => 1000, 'growth' => 2.5],
            ['month' => 'Feb', 'attendance' => 1020, 'target' => 1000, 'growth' => 4.1],
            ['month' => 'Mar', 'attendance' => 1150, 'target' => 1100, 'growth' => 12.7],
            ['month' => 'Apr', 'attendance' => 1080, 'target' => 1100, 'growth' => -6.1],
            ['month' => 'May', 'attendance' => 1200, 'target' => 1150, 'growth' => 11.1],
            ['month' => 'Jun', 'attendance' => 1180, 'target' => 1150, 'growth' => -1.7],
            ['month' => 'Jul', 'attendance' => 1050, 'target' => 1100, 'growth' => -11.0],
            ['month' => 'Aug', 'attendance' => 1100, 'target' => 1100, 'growth' => 4.8],
            ['month' => 'Sep', 'attendance' => 1250, 'target' => 1200, 'growth' => 13.6],
            ['month' => 'Oct', 'attendance' => 1300, 'target' => 1250, 'growth' => 4.0],
            ['month' => 'Nov', 'attendance' => 1280, 'target' => 1250, 'growth' => -1.5],
            ['month' => 'Dec', 'attendance' => 1400, 'target' => 1300, 'growth' => 9.4],
        ];

        return response()->json([
            'data' => $monthlyData,
            'year' => $year,
        ]);
    }

    /**
     * Get service-specific attendance data.
     */
    public function serviceAttendance(Request $request): JsonResponse
    {
        $serviceType = $request->get('service_type', 'all');
        $period = $request->get('period', 'month');

        // Mock data for service-specific attendance
        $serviceData = [
            [
                'service_type' => 'Sunday Morning',
                'average_attendance' => 245,
                'total_services' => 52,
                'highest' => 320,
                'lowest' => 180,
                'trend' => 'up',
            ],
            [
                'service_type' => 'Wednesday Evening',
                'average_attendance' => 89,
                'total_services' => 48,
                'highest' => 120,
                'lowest' => 65,
                'trend' => 'stable',
            ],
            [
                'service_type' => 'Youth Service',
                'average_attendance' => 45,
                'total_services' => 24,
                'highest' => 65,
                'lowest' => 28,
                'trend' => 'up',
            ],
        ];

        return response()->json([
            'data' => $serviceData,
            'filters' => [
                'service_type' => $serviceType,
                'period' => $period,
            ],
        ]);
    }

    /**
     * Get member attendance tracking data.
     */
    public function memberAttendance(Request $request): JsonResponse
    {
        $memberId = $request->get('member_id');
        $period = $request->get('period', 'month');

        // Mock data for member attendance
        $memberData = [
            [
                'member_id' => 1,
                'name' => 'John Smith',
                'total_services' => 48,
                'attended' => 42,
                'attendance_rate' => 87.5,
                'streak' => 6,
                'last_attended' => '2025-01-08',
            ],
            [
                'member_id' => 2,
                'name' => 'Sarah Johnson',
                'total_services' => 48,
                'attended' => 45,
                'attendance_rate' => 93.8,
                'streak' => 12,
                'last_attended' => '2025-01-08',
            ],
        ];

        return response()->json([
            'data' => $memberData,
            'filters' => [
                'member_id' => $memberId,
                'period' => $period,
            ],
        ]);
    }

    /**
     * Get attendance comparison data.
     */
    public function attendanceComparison(Request $request): JsonResponse
    {
        $comparisonType = $request->get('type', 'year_over_year'); // year_over_year, month_over_month

        // Mock comparison data
        $comparisonData = [
            'current_period' => [
                'label' => '2025',
                'total_attendance' => 14280,
                'average_per_service' => 245,
                'growth_rate' => 8.5,
            ],
            'previous_period' => [
                'label' => '2024',
                'total_attendance' => 13150,
                'average_per_service' => 225,
                'growth_rate' => 3.2,
            ],
            'monthly_comparison' => [
                ['month' => 'Jan', 'current' => 980, 'previous' => 920],
                ['month' => 'Feb', 'current' => 1020, 'previous' => 950],
                ['month' => 'Mar', 'current' => 1150, 'previous' => 1080],
                ['month' => 'Apr', 'current' => 1080, 'previous' => 1020],
                ['month' => 'May', 'current' => 1200, 'previous' => 1100],
                ['month' => 'Jun', 'current' => 1180, 'previous' => 1150],
            ],
        ];

        return response()->json([
            'data' => $comparisonData,
            'comparison_type' => $comparisonType,
        ]);
    }

    /**
     * Export report data.
     */
    public function exportReport(Request $request): JsonResponse
    {
        $reportType = $request->get('report_type');
        $format = $request->get('format', 'pdf'); // pdf, excel

        // This will be implemented with actual export logic
        return response()->json([
            'success' => true,
            'message' => 'Report export initiated',
            'download_url' => '/reports/download/' . uniqid(),
        ]);
    }

    /**
     * Get basic report statistics.
     */
    private function getReportStats(): array
    {
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;
        $lastMonth = Carbon::now()->subMonth();

        // Total services this year
        $totalServices = AttendanceRecord::whereYear('service_date', $currentYear)
            ->distinct('service_id', 'service_date')
            ->count();

        // Average attendance
        $averageAttendance = AttendanceRecord::whereYear('service_date', $currentYear)
            ->where('status', '!=', 'absent')
            ->selectRaw('service_id, service_date, COUNT(*) as attendance')
            ->groupBy('service_id', 'service_date')
            ->get()
            ->avg('attendance');

        // Highest and lowest attendance
        $attendanceStats = AttendanceRecord::whereYear('service_date', $currentYear)
            ->where('status', '!=', 'absent')
            ->selectRaw('service_id, service_date, COUNT(*) as attendance')
            ->groupBy('service_id', 'service_date')
            ->get();

        $highestAttendance = $attendanceStats->max('attendance') ?? 0;
        $lowestAttendance = $attendanceStats->min('attendance') ?? 0;

        // Growth rate (year over year)
        $currentYearTotal = AttendanceRecord::whereYear('service_date', $currentYear)
            ->where('status', '!=', 'absent')
            ->count();

        $lastYearTotal = AttendanceRecord::whereYear('service_date', $currentYear - 1)
            ->where('status', '!=', 'absent')
            ->count();

        $growthRate = $lastYearTotal > 0
            ? (($currentYearTotal - $lastYearTotal) / $lastYearTotal) * 100
            : 0;

        // Active members (users with attendance in the last 3 months)
        $activeMembers = AttendanceRecord::where('service_date', '>=', Carbon::now()->subMonths(3))
            ->where('status', '!=', 'absent')
            ->distinct('user_id')
            ->count();

        // Regular attendees (80%+ attendance rate in last 3 months)
        $regularAttendees = User::whereHas('attendanceRecords', function ($query) {
            $query->where('service_date', '>=', Carbon::now()->subMonths(3))
                  ->where('status', '!=', 'absent');
        })->get()->filter(function ($user) {
            $totalServices = AttendanceRecord::where('service_date', '>=', Carbon::now()->subMonths(3))
                ->distinct('service_id', 'service_date')
                ->count();

            $userAttendance = $user->attendanceRecords()
                ->where('service_date', '>=', Carbon::now()->subMonths(3))
                ->where('status', '!=', 'absent')
                ->count();

            return $totalServices > 0 && ($userAttendance / $totalServices) >= 0.8;
        })->count();

        // New members this month (users with first attendance this month)
        $newMembersThisMonth = User::whereHas('attendanceRecords', function ($query) use ($currentMonth, $currentYear) {
            $query->whereMonth('service_date', $currentMonth)
                  ->whereYear('service_date', $currentYear);
        })->whereDoesntHave('attendanceRecords', function ($query) use ($currentMonth, $currentYear) {
            $query->where(function ($q) use ($currentMonth, $currentYear) {
                $q->whereMonth('service_date', '<', $currentMonth)
                  ->whereYear('service_date', $currentYear);
            })->orWhere(function ($q) use ($currentYear) {
                $q->whereYear('service_date', '<', $currentYear);
            });
        })->count();

        return [
            'total_services_this_year' => $totalServices,
            'average_attendance' => round($averageAttendance ?? 0),
            'highest_attendance' => $highestAttendance,
            'lowest_attendance' => $lowestAttendance,
            'growth_rate' => round($growthRate, 1),
            'active_members' => $activeMembers,
            'regular_attendees' => $regularAttendees,
            'new_members_this_month' => $newMembersThisMonth,
        ];
    }
}

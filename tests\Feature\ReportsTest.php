<?php

namespace Tests\Feature;

use App\Models\AttendanceRecord;
use App\Models\Service;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReportsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        
        // Create test services
        $this->service = Service::create([
            'name' => 'Sunday Morning Worship',
            'description' => 'Main Sunday morning worship service',
            'service_type' => 'sunday_morning',
            'day_of_week' => 0,
            'start_time' => '10:00:00',
            'end_time' => '11:30:00',
            'is_active' => true,
            'target_attendance' => 250,
            'location' => 'Main Sanctuary',
            'recurring' => true,
            'frequency' => 'weekly',
        ]);
    }

    public function test_reports_page_loads_successfully(): void
    {
        $response = $this->actingAs($this->user)->get('/reports');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('reports')
                 ->has('stats')
        );
    }

    public function test_weekly_attendance_api_returns_data(): void
    {
        // Create some attendance records
        AttendanceRecord::create([
            'user_id' => $this->user->id,
            'service_id' => $this->service->id,
            'service_date' => Carbon::now()->startOfWeek(),
            'check_in_time' => Carbon::now()->startOfWeek()->addHours(10),
            'status' => 'present',
            'check_in_method' => 'manual',
        ]);

        $response = $this->actingAs($this->user)->get('/reports/weekly-attendance');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'date',
                    'service_type',
                    'attendance',
                    'target',
                    'percentage',
                ]
            ],
            'period' => [
                'start',
                'end',
            ]
        ]);
    }

    public function test_monthly_trends_api_returns_data(): void
    {
        $response = $this->actingAs($this->user)->get('/reports/monthly-trends');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'month',
                    'attendance',
                    'target',
                    'growth',
                ]
            ],
            'year'
        ]);
    }

    public function test_service_attendance_api_returns_data(): void
    {
        $response = $this->actingAs($this->user)->get('/reports/service-attendance');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'service_type',
                    'average_attendance',
                    'total_services',
                    'highest',
                    'lowest',
                    'trend',
                ]
            ],
            'filters'
        ]);
    }

    public function test_member_attendance_api_returns_data(): void
    {
        $response = $this->actingAs($this->user)->get('/reports/member-attendance');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'member_id',
                    'name',
                    'total_services',
                    'attended',
                    'attendance_rate',
                    'streak',
                    'last_attended',
                ]
            ],
            'filters'
        ]);
    }

    public function test_attendance_comparison_api_returns_data(): void
    {
        $response = $this->actingAs($this->user)->get('/reports/attendance-comparison');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'current_period' => [
                    'label',
                    'total_attendance',
                    'average_per_service',
                    'growth_rate',
                ],
                'previous_period' => [
                    'label',
                    'total_attendance',
                    'average_per_service',
                    'growth_rate',
                ],
                'monthly_comparison' => [
                    '*' => [
                        'month',
                        'current',
                        'previous',
                    ]
                ]
            ],
            'comparison_type'
        ]);
    }

    public function test_export_report_endpoint_works(): void
    {
        $response = $this->actingAs($this->user)->post('/reports/export', [
            'report_type' => 'weekly-attendance',
            'format' => 'pdf',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'download_url',
        ]);
    }

    public function test_reports_stats_calculation(): void
    {
        // Create some test data
        $users = User::factory(10)->create();
        
        foreach ($users as $user) {
            AttendanceRecord::create([
                'user_id' => $user->id,
                'service_id' => $this->service->id,
                'service_date' => Carbon::now()->subDays(7),
                'check_in_time' => Carbon::now()->subDays(7)->addHours(10),
                'status' => 'present',
                'check_in_method' => 'manual',
            ]);
        }

        $response = $this->actingAs($this->user)->get('/reports');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('stats.total_services_this_year')
                 ->has('stats.average_attendance')
                 ->has('stats.highest_attendance')
                 ->has('stats.lowest_attendance')
                 ->has('stats.growth_rate')
                 ->has('stats.active_members')
                 ->has('stats.regular_attendees')
                 ->has('stats.new_members_this_month')
        );
    }
}

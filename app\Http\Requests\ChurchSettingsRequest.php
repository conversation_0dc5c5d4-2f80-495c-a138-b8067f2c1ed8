<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChurchSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Church Information
            'church_name' => ['nullable', 'string', 'max:255'],
            'church_address' => ['nullable', 'string'],
            'church_phone' => ['nullable', 'string', 'max:20'],
            'church_email' => ['nullable', 'email', 'max:255'],
            'church_website' => ['nullable', 'url', 'max:255'],
            'social_media_links' => ['nullable', 'array'],
            'social_media_links.facebook' => ['nullable', 'url'],
            'social_media_links.twitter' => ['nullable', 'url'],
            'social_media_links.instagram' => ['nullable', 'url'],
            'social_media_links.youtube' => ['nullable', 'url'],
            'church_description' => ['nullable', 'string'],
            'mission_statement' => ['nullable', 'string'],
            'service_times' => ['nullable', 'array'],
            'leadership_info' => ['nullable', 'array'],

            // Geolocation & Geofence
            'latitude' => ['nullable', 'numeric', 'between:-90,90'],
            'longitude' => ['nullable', 'numeric', 'between:-180,180'],
            'geofence_radius' => ['nullable', 'integer', 'min:10', 'max:10000'],

            // Attendance Settings
            'attendance_preferences' => ['nullable', 'array'],
            'manual_checkin_settings' => ['nullable', 'array'],
            'notification_settings' => ['nullable', 'array'],
            'attendance_report_configs' => ['nullable', 'array'],

            // System Settings
            'user_roles_permissions' => ['nullable', 'array'],
            'notification_preferences' => ['nullable', 'array'],
            'backup_settings' => ['nullable', 'array'],
            'privacy_security_configs' => ['nullable', 'array'],

            // Integration Settings
            'mobile_app_config' => ['nullable', 'array'],
            'third_party_integrations' => ['nullable', 'array'],
            'api_keys' => ['nullable', 'array'],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'church_email.email' => 'Please enter a valid email address for the church.',
            'church_website.url' => 'Please enter a valid website URL.',
            'latitude.between' => 'Latitude must be between -90 and 90 degrees.',
            'longitude.between' => 'Longitude must be between -180 and 180 degrees.',
            'geofence_radius.min' => 'Geofence radius must be at least 10 meters.',
            'geofence_radius.max' => 'Geofence radius cannot exceed 10,000 meters.',
        ];
    }
}

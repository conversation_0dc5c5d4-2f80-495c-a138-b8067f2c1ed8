<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the super admin user with the specified credentials
        $superAdmin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('Gilash@123'),
                'is_super_admin' => true,
                'tenant_id' => null, // Super admins don't belong to specific tenants
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Super admin created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: Gilash@123');
        $this->command->warn('Please change the password after first login in production!');
    }
}

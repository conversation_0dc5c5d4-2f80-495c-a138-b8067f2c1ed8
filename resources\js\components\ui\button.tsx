import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import type { VelonicBrandColor, SemanticColor } from "@/types/theme"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow,background-color] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // Velonic soft variants
        soft: "bg-primary/10 text-primary hover:bg-primary/20 border border-primary/20",
        "soft-secondary": "bg-secondary/10 text-secondary-foreground hover:bg-secondary/20 border border-secondary/20",
        "soft-success": "bg-blue-subtle text-blue hover:bg-blue/20 border border-blue/20",
        "soft-info": "bg-indigo-subtle text-indigo hover:bg-indigo/20 border border-indigo/20",
        "soft-warning": "bg-yellow-subtle text-yellow hover:bg-yellow/20 border border-yellow/20",
        "soft-danger": "bg-destructive/10 text-destructive hover:bg-destructive/20 border border-destructive/20",
        // Velonic outline variants
        "outline-primary": "border-2 border-primary text-primary bg-transparent hover:bg-primary/5",
        "outline-secondary": "border-2 border-secondary text-secondary-foreground bg-transparent hover:bg-secondary/5",
        "outline-success": "border-2 border-blue text-blue bg-transparent hover:bg-blue/5",
        "outline-info": "border-2 border-indigo text-indigo bg-transparent hover:bg-indigo/5",
        "outline-warning": "border-2 border-yellow text-yellow bg-transparent hover:bg-yellow/5",
        "outline-danger": "border-2 border-destructive text-destructive bg-transparent hover:bg-destructive/5",
        // Velonic brand color variants
        blue: "bg-blue text-white shadow-xs hover:bg-blue/90",
        indigo: "bg-indigo text-white shadow-xs hover:bg-indigo/90",
        purple: "bg-purple text-white shadow-xs hover:bg-purple/90",
        pink: "bg-pink text-white shadow-xs hover:bg-pink/90",
        red: "bg-red text-white shadow-xs hover:bg-red/90",
        orange: "bg-orange text-white shadow-xs hover:bg-orange/90",
        yellow: "bg-yellow text-gray-900 shadow-xs hover:bg-yellow/90",
        green: "bg-green text-white shadow-xs hover:bg-green/90",
        teal: "bg-teal text-white shadow-xs hover:bg-teal/90",
        cyan: "bg-cyan text-white shadow-xs hover:bg-cyan/90",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        xl: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",
        icon: "size-9",
        "icon-sm": "size-8",
        "icon-lg": "size-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

interface ButtonProps extends 
  React.ComponentProps<"button">,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
  color?: VelonicBrandColor | SemanticColor
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

function Button({
  className,
  variant,
  size,
  color,
  loading,
  leftIcon,
  rightIcon,
  children,
  disabled,
  asChild = false,
  ...props
}: ButtonProps) {
  const Comp = asChild ? Slot : "button"

  // Handle dynamic color variants
  const colorVariant = React.useMemo(() => {
    if (color && !variant) {
      // Default to the color as variant if no variant specified
      return color as any;
    }
    if (color && variant === 'soft') {
      return `soft-${color}` as any;
    }
    if (color && variant === 'outline') {
      return `outline-${color}` as any;
    }
    return variant;
  }, [variant, color]);

  const isLoading = loading && !disabled;
  const isDisabled = disabled || loading;

  return (
    <Comp
      data-slot="button"
      className={cn(
        buttonVariants({ variant: colorVariant, size }),
        isLoading && "relative text-transparent",
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {/* Loading spinner overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        </div>
      )}
      
      {/* Left icon */}
      {leftIcon && !isLoading && (
        <span className="shrink-0">{leftIcon}</span>
      )}
      
      {/* Button content */}
      {children}
      
      {/* Right icon */}
      {rightIcon && !isLoading && (
        <span className="shrink-0">{rightIcon}</span>
      )}
    </Comp>
  )
}

export { Button, buttonVariants, type ButtonProps }

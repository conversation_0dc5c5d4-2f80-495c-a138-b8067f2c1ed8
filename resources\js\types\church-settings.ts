export interface ChurchSettings {
    id?: number;
    church_name?: string;
    church_address?: string;
    church_phone?: string;
    church_email?: string;
    church_website?: string;
    social_media_links?: {
        facebook?: string;
        twitter?: string;
        instagram?: string;
        youtube?: string;
    };
    church_description?: string;
    mission_statement?: string;
    service_times?: Array<{
        day: string;
        time: string;
        service_type: string;
    }>;
    leadership_info?: Array<{
        name: string;
        position: string;
        bio?: string;
    }>;
    latitude?: number;
    longitude?: number;
    geofence_radius?: number;
    attendance_preferences?: {
        auto_checkin?: boolean;
        notification_enabled?: boolean;
        late_threshold?: number;
    };
    manual_checkin_settings?: {
        enabled?: boolean;
        admin_only?: boolean;
        time_window?: number;
    };
    notification_settings?: {
        email_enabled?: boolean;
        sms_enabled?: boolean;
        push_enabled?: boolean;
    };
    mobile_app_config?: {
        app_name?: string;
        theme_color?: string;
        notification_enabled?: boolean;
    };
}

export interface ChurchSettingsPageProps {
    settings: ChurchSettings;
}

export interface ServiceTime {
    day: string;
    time: string;
    service_type: string;
}

export interface LeadershipInfo {
    name: string;
    position: string;
    bio?: string;
}

export interface SocialMediaLinks {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
}

export interface AttendancePreferences {
    auto_checkin?: boolean;
    notification_enabled?: boolean;
    late_threshold?: number;
}

export interface ManualCheckinSettings {
    enabled?: boolean;
    admin_only?: boolean;
    time_window?: number;
}

export interface NotificationSettings {
    email_enabled?: boolean;
    sms_enabled?: boolean;
    push_enabled?: boolean;
}

export interface MobileAppConfig {
    app_name?: string;
    theme_color?: string;
    notification_enabled?: boolean;
}

export interface CoordinateErrors {
    latitude?: string;
    longitude?: string;
    geofence_radius?: string;
}

// Common props for form components
export interface BaseFormComponentProps {
    formData: ChurchSettings;
    onInputChange: (field: string, value: any) => void;
    onNestedChange: (parent: string, field: string, value: any) => void;
}

// Specific component props
export interface ChurchInformationFormProps extends BaseFormComponentProps {}

export interface ExtendedChurchInformationFormProps extends ChurchInformationFormProps, ServiceTimesManagerProps, LeadershipManagerProps {}

export interface SocialMediaLinksFormProps extends BaseFormComponentProps {}

export interface ServiceTimesManagerProps extends BaseFormComponentProps {
    onServiceTimeChange: (index: number, field: string, value: string) => void;
    onAddServiceTime: () => void;
    onRemoveServiceTime: (index: number) => void;
}

export interface LeadershipManagerProps extends BaseFormComponentProps {
    onLeadershipChange: (index: number, field: string, value: string) => void;
    onAddLeadership: () => void;
    onRemoveLeadership: (index: number) => void;
}

export interface LocationSettingsFormProps extends BaseFormComponentProps {
    coordinateErrors: CoordinateErrors;
    onCoordinateChange: (field: 'latitude' | 'longitude' | 'geofence_radius', value: string) => void;
    onTestGeofence: () => void;
}

export interface AttendanceSettingsFormProps extends BaseFormComponentProps {}

export interface NotificationSettingsFormProps extends BaseFormComponentProps {}

export interface IntegrationSettingsFormProps extends BaseFormComponentProps {}

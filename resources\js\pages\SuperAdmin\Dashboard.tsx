import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import SuperAdminLayout from '@/layouts/super-admin-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { 
    Building2, 
    Users, 
    Activity, 
    TrendingUp, 
    AlertTriangle,
    CheckCircle,
    Clock,
    Database
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Super Admin Dashboard',
        href: '/super-admin/dashboard',
    },
];

// Mock data - in real implementation, this would come from props
const systemStats = {
    totalTenants: 24,
    activeTenants: 22,
    totalUsers: 1247,
    systemHealth: 'healthy',
    databaseSize: '2.4 GB',
    uptime: '99.9%'
};

const recentActivity = [
    { id: 1, action: 'New tenant registered', tenant: 'Grace Community Church', time: '2 hours ago', type: 'success' },
    { id: 2, action: 'Tenant suspended', tenant: 'Faith Baptist Church', time: '4 hours ago', type: 'warning' },
    { id: 3, action: 'System backup completed', tenant: 'System', time: '6 hours ago', type: 'info' },
    { id: 4, action: 'User limit exceeded', tenant: 'Hope Methodist Church', time: '8 hours ago', type: 'error' },
];

const tenantAlerts = [
    { id: 1, message: 'Trial ending in 3 days', tenant: 'New Life Church', severity: 'warning' },
    { id: 2, message: 'Payment failed', tenant: 'Community Baptist', severity: 'error' },
    { id: 3, message: 'High usage detected', tenant: 'Grace Fellowship', severity: 'info' },
];

export default function SuperAdminDashboard() {
    return (
        <SuperAdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Super Admin Dashboard" />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Super Admin Dashboard</h1>
                        <p className="text-muted-foreground">
                            Monitor and manage the entire Flockin system
                        </p>
                    </div>
                    <Button>
                        <Database className="mr-2 h-4 w-4" />
                        System Backup
                    </Button>
                </div>

                {/* System Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemStats.totalTenants}</div>
                            <p className="text-xs text-muted-foreground">
                                +2 from last month
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemStats.activeTenants}</div>
                            <p className="text-xs text-muted-foreground">
                                {((systemStats.activeTenants / systemStats.totalTenants) * 100).toFixed(1)}% active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemStats.totalUsers.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                +12% from last month
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
                            <TrendingUp className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemStats.uptime}</div>
                            <p className="text-xs text-muted-foreground">
                                Last 30 days
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Content Grid */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Recent Activity */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Activity className="h-5 w-5" />
                                Recent Activity
                            </CardTitle>
                            <CardDescription>
                                Latest system events and tenant activities
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentActivity.map((activity) => (
                                    <div key={activity.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">{activity.action}</p>
                                            <p className="text-xs text-muted-foreground">{activity.tenant}</p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge 
                                                variant={
                                                    activity.type === 'success' ? 'default' :
                                                    activity.type === 'warning' ? 'secondary' :
                                                    activity.type === 'error' ? 'destructive' : 'outline'
                                                }
                                            >
                                                {activity.type}
                                            </Badge>
                                            <span className="text-xs text-muted-foreground">{activity.time}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Tenant Alerts */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5" />
                                Tenant Alerts
                            </CardTitle>
                            <CardDescription>
                                Issues requiring attention
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {tenantAlerts.map((alert) => (
                                    <div key={alert.id} className="flex items-center justify-between">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium">{alert.message}</p>
                                            <p className="text-xs text-muted-foreground">{alert.tenant}</p>
                                        </div>
                                        <Badge 
                                            variant={
                                                alert.severity === 'error' ? 'destructive' :
                                                alert.severity === 'warning' ? 'secondary' : 'outline'
                                            }
                                        >
                                            {alert.severity}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* System Health */}
                <Card>
                    <CardHeader>
                        <CardTitle>System Health</CardTitle>
                        <CardDescription>
                            Overall system performance and resource usage
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="flex items-center gap-2">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                <div>
                                    <p className="text-sm font-medium">Database</p>
                                    <p className="text-xs text-muted-foreground">{systemStats.databaseSize}</p>
                                </div>
                            </div>
                            <div className="flex items-center gap-2">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                <div>
                                    <p className="text-sm font-medium">API Status</p>
                                    <p className="text-xs text-muted-foreground">All services operational</p>
                                </div>
                            </div>
                            <div className="flex items-center gap-2">
                                <Clock className="h-5 w-5 text-blue-600" />
                                <div>
                                    <p className="text-sm font-medium">Last Backup</p>
                                    <p className="text-xs text-muted-foreground">2 hours ago</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SuperAdminLayout>
    );
}

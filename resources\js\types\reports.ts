export interface ReportStats {
    total_services_this_year: number;
    average_attendance: number;
    highest_attendance: number;
    lowest_attendance: number;
    growth_rate: number;
    active_members: number;
    regular_attendees: number;
    new_members_this_month: number;
}

export interface WeeklyAttendanceData {
    date: string;
    service_type: string;
    attendance: number;
    target: number;
    percentage: number;
}

export interface MonthlyTrendData {
    month: string;
    attendance: number;
    target: number;
    growth: number;
}

export interface ServiceAttendanceData {
    service_type: string;
    average_attendance: number;
    total_services: number;
    highest: number;
    lowest: number;
    trend: 'up' | 'down' | 'stable';
}

export interface MemberAttendanceData {
    member_id: number;
    name: string;
    total_services: number;
    attended: number;
    attendance_rate: number;
    streak: number;
    last_attended: string;
}

export interface AttendanceComparisonData {
    current_period: {
        label: string;
        total_attendance: number;
        average_per_service: number;
        growth_rate: number;
    };
    previous_period: {
        label: string;
        total_attendance: number;
        average_per_service: number;
        growth_rate: number;
    };
    monthly_comparison: Array<{
        month: string;
        current: number;
        previous: number;
    }>;
}

export interface ReportFilters {
    start_date?: string;
    end_date?: string;
    service_type?: string;
    member_id?: number;
    period?: 'week' | 'month' | 'quarter' | 'year';
    comparison_type?: 'year_over_year' | 'month_over_month';
}

export interface ExportOptions {
    report_type: string;
    format: 'pdf' | 'excel';
    filters?: ReportFilters;
}

export interface ChartDataPoint {
    name: string;
    value: number;
    color?: string;
}

export interface AttendanceChartData {
    labels: string[];
    datasets: Array<{
        label: string;
        data: number[];
        backgroundColor?: string;
        borderColor?: string;
        fill?: boolean;
    }>;
}

export interface ReportsPageProps {
    stats: ReportStats;
}

export interface TabConfig {
    key: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
}

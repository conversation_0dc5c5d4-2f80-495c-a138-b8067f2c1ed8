import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Icon } from '@/components/ui/icon';
import { type RecentActivity } from '@/data/church-dashboard-data';
import { Activity, Calendar, DollarSign, UserPlus, Users } from 'lucide-react';

interface RecentActivitiesProps {
    activities: RecentActivity[];
    showAll?: boolean;
}

const activityIcons = {
    member_joined: UserPlus,
    donation: DollarSign,
    attendance: Users,
    event_created: Calendar,
};

const activityColors = {
    member_joined: 'bg-success/10 text-success',
    donation: 'bg-info/10 text-info',
    attendance: 'bg-primary/10 text-primary',
    event_created: 'bg-warning/10 text-warning',
};

export function RecentActivities({ activities, showAll = false }: RecentActivitiesProps) {
    const displayedActivities = showAll ? activities : activities.slice(0, 6);

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map((n) => n[0])
            .join('')
            .toUpperCase();
    };

    return (
        <Card className="shadow-sm transition-all duration-200 hover:shadow-md">
            <div className="p-5">
                <div className="mb-5 flex items-center justify-between">
                    <h3 className="flex items-center gap-2 text-lg font-semibold text-foreground">
                        <Icon iconNode={Activity} className="h-5 w-5" />
                        Recent Activities
                    </h3>
                </div>

                <div className="space-y-3">
                    {displayedActivities.map((activity) => {
                        const IconComponent = activityIcons[activity.type];

                        return (
                            <div
                                key={activity.id}
                                className="flex items-start gap-3 rounded border border-border/50 p-3 transition-all duration-200 hover:border-border hover:bg-muted/30"
                            >
                                <div className={`flex-shrink-0 rounded-full p-2 ${activityColors[activity.type]}`}>
                                    <Icon iconNode={IconComponent} className="h-3 w-3" />
                                </div>

                                <div className="min-w-0 flex-1">
                                    <div className="flex items-start justify-between gap-2">
                                        <div className="min-w-0 flex-1">
                                            <p className="truncate text-sm font-medium text-foreground">{activity.description}</p>
                                            <div className="mt-1 flex items-center gap-2">
                                                {activity.user && (
                                                    <Avatar className="h-6 w-6 flex-shrink-0">
                                                        <div className="flex h-full w-full items-center justify-center bg-muted text-xs font-medium">
                                                            {getInitials(activity.user)}
                                                        </div>
                                                    </Avatar>
                                                )}
                                                <span className="truncate text-xs text-muted-foreground">{activity.timestamp}</span>
                                            </div>
                                        </div>
                                        {activity.amount && <span className="flex-shrink-0 text-sm font-medium text-success">{activity.amount}</span>}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {!showAll && activities.length > 6 && (
                    <div className="mt-4 border-t border-border/50 pt-4 text-center">
                        <Button variant="ghost" size="sm" className="text-xs text-muted-foreground hover:text-foreground">
                            View all activities
                        </Button>
                    </div>
                )}
            </div>
        </Card>
    );
}

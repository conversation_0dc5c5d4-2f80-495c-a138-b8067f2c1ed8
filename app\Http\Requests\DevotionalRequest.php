<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DevotionalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $devotionalId = $this->route('devotional')?->id;

        return [
            'title' => ['required', 'string', 'max:255'],
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('devotionals', 'slug')->ignore($devotionalId),
            ],
            'content' => ['required', 'string'],
            'summary' => ['nullable', 'string', 'max:500'],
            'author' => ['nullable', 'string', 'max:255'],
            'devotional_date' => ['nullable', 'date'],

            // Scripture and spiritual content
            'scripture_references' => ['nullable', 'array'],
            'scripture_references.*' => ['string', 'max:100'],
            'key_verse' => ['nullable', 'string', 'max:1000'],
            'key_verse_reference' => ['nullable', 'string', 'max:100'],
            'prayer' => ['nullable', 'string', 'max:2000'],
            'reflection_questions' => ['nullable', 'string', 'max:2000'],

            // Content organization
            'category_id' => ['nullable', 'exists:content_categories,id'],
            'themes' => ['nullable', 'array'],
            'themes.*' => ['string', 'max:100'],
            'series_name' => ['nullable', 'string', 'max:255'],
            'series_order' => ['nullable', 'integer', 'min:1'],

            // Media attachments
            'featured_image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:10240', // 10MB
            ],
            'audio_file' => [
                'nullable',
                'file',
                'mimes:mp3,wav,ogg,m4a',
                'max:51200', // 50MB
            ],

            // Publishing and scheduling
            'status' => ['required', 'in:draft,published,scheduled,archived'],
            'is_featured' => ['boolean'],
            'published_at' => ['nullable', 'date'],
            'scheduled_for' => [
                'nullable',
                'date',
                'after:now',
                Rule::requiredIf($this->input('status') === 'scheduled'),
            ],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['string', 'max:50'],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Devotional title is required.',
            'title.max' => 'Devotional title cannot exceed 255 characters.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'slug.regex' => 'Slug can only contain lowercase letters, numbers, and hyphens.',
            'content.required' => 'Devotional content is required.',
            'summary.max' => 'Summary cannot exceed 500 characters.',
            'author.max' => 'Author name cannot exceed 255 characters.',

            // Scripture validation messages
            'scripture_references.*.max' => 'Each scripture reference cannot exceed 100 characters.',
            'key_verse.max' => 'Key verse cannot exceed 1000 characters.',
            'key_verse_reference.max' => 'Key verse reference cannot exceed 100 characters.',
            'prayer.max' => 'Prayer cannot exceed 2000 characters.',
            'reflection_questions.max' => 'Reflection questions cannot exceed 2000 characters.',

            // Organization messages
            'category_id.exists' => 'Selected category does not exist.',
            'themes.*.max' => 'Each theme cannot exceed 100 characters.',
            'series_name.max' => 'Series name cannot exceed 255 characters.',
            'series_order.min' => 'Series order must be at least 1.',

            // File validation messages
            'featured_image.image' => 'Featured image must be an image file.',
            'featured_image.mimes' => 'Featured image must be in one of these formats: JPEG, PNG, JPG, GIF, WebP.',
            'featured_image.max' => 'Featured image file size cannot exceed 10MB.',
            'audio_file.file' => 'Please upload a valid audio file.',
            'audio_file.mimes' => 'Audio file must be in one of these formats: MP3, WAV, OGG, M4A.',
            'audio_file.max' => 'Audio file size cannot exceed 50MB.',

            // Publishing messages
            'status.required' => 'Devotional status is required.',
            'status.in' => 'Invalid devotional status selected.',
            'scheduled_for.after' => 'Scheduled date must be in the future.',
            'scheduled_for.required' => 'Scheduled date is required when status is set to scheduled.',
            'tags.*.max' => 'Each tag cannot exceed 50 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'featured_image' => 'featured image',
            'audio_file' => 'audio file',
            'is_featured' => 'featured status',
            'published_at' => 'publish date',
            'scheduled_for' => 'scheduled date',
            'category_id' => 'category',
            'series_order' => 'series order',
            'devotional_date' => 'devotional date',
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Church name
            $table->string('slug')->unique(); // URL slug for subdomain
            $table->string('domain')->nullable(); // Custom domain if any
            $table->string('database_name')->nullable(); // For future database separation
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->string('plan_type')->default('basic'); // For future billing
            $table->json('settings')->nullable(); // Tenant-specific settings
            $table->timestamp('trial_ends_at')->nullable(); // For future billing
            $table->timestamps();

            // Indexes for performance
            $table->index('slug');
            $table->index('status');
            $table->index('domain');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { 
    BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
    LineChart, Line, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';
import { 
    TrendingUp, TrendingDown, Users, DollarSign, Calendar, 
    Clock, MapPin, Heart, Download, Filter 
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Analytics',
        href: '/admin/analytics',
    },
];

// Sample analytics data
const attendanceData = [
    { month: 'Jan', attendance: 850, target: 900 },
    { month: 'Feb', attendance: 920, target: 900 },
    { month: 'Mar', attendance: 880, target: 900 },
    { month: 'Apr', attendance: 1050, target: 900 },
    { month: 'May', attendance: 1120, target: 900 },
    { month: 'Jun', attendance: 980, target: 900 },
    { month: 'Jul', attendance: 1200, target: 900 },
    { month: 'Aug', attendance: 1150, target: 900 },
    { month: 'Sep', attendance: 1080, target: 900 },
    { month: 'Oct', attendance: 1250, target: 900 },
    { month: 'Nov', attendance: 1180, target: 900 },
    { month: 'Dec', attendance: 1300, target: 900 },
];

const donationData = [
    { month: 'Jan', amount: 24500 },
    { month: 'Feb', amount: 26800 },
    { month: 'Mar', amount: 23200 },
    { month: 'Apr', amount: 28900 },
    { month: 'May', amount: 31200 },
    { month: 'Jun', amount: 29800 },
    { month: 'Jul', amount: 33500 },
    { month: 'Aug', amount: 32100 },
    { month: 'Sep', amount: 30800 },
    { month: 'Oct', amount: 35200 },
    { month: 'Nov', amount: 34600 },
    { month: 'Dec', amount: 38900 },
];

const membershipGrowthData = [
    { month: 'Jan', members: 1180, newMembers: 12, leftMembers: 3 },
    { month: 'Feb', members: 1195, newMembers: 18, leftMembers: 3 },
    { month: 'Mar', members: 1208, newMembers: 15, leftMembers: 2 },
    { month: 'Apr', members: 1225, newMembers: 19, leftMembers: 2 },
    { month: 'May', members: 1248, newMembers: 25, leftMembers: 2 },
    { month: 'Jun', members: 1265, newMembers: 20, leftMembers: 3 },
];

const serviceAttendanceData = [
    { service: 'Sunday Morning', attendance: 987, percentage: 78 },
    { service: 'Sunday Evening', attendance: 456, percentage: 36 },
    { service: 'Wednesday Prayer', attendance: 234, percentage: 18 },
    { service: 'Youth Service', attendance: 156, percentage: 12 },
    { service: 'Small Groups', attendance: 289, percentage: 23 },
];

const ageGroupData = [
    { name: 'Children (0-12)', value: 285, color: '#34b0e0' },
    { name: 'Youth (13-17)', value: 156, color: '#4489e4' },
    { name: 'Young Adults (18-35)', value: 398, color: '#716cb0' },
    { name: 'Adults (36-55)', value: 287, color: '#f24f7c' },
    { name: 'Seniors (55+)', value: 122, color: '#f7931e' },
];

const keyMetrics = [
    {
        title: 'Total Members',
        value: '1,248',
        change: '+12.5%',
        trend: 'up',
        icon: Users,
        color: 'bg-cyan',
    },
    {
        title: 'Monthly Donations',
        value: '$38,900',
        change: '+15.3%',
        trend: 'up',
        icon: DollarSign,
        color: 'bg-blue',
    },
    {
        title: 'Avg. Attendance',
        value: '987',
        change: '+8.2%',
        trend: 'up',
        icon: Calendar,
        color: 'bg-purple',
    },
    {
        title: 'Active Volunteers',
        value: '156',
        change: '-2.1%',
        trend: 'down',
        icon: Heart,
        color: 'bg-pink',
    },
];

export default function AdminAnalytics() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Admin Analytics" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
                        <p className="text-muted-foreground">Comprehensive insights into church operations and growth</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {/* Key Metrics Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {keyMetrics.map((metric, index) => (
                        <Card key={index} className="shadow-sm">
                            <div className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-2">
                                        <p className="text-sm font-medium text-muted-foreground">{metric.title}</p>
                                        <p className="text-2xl font-bold">{metric.value}</p>
                                        <div className="flex items-center gap-1">
                                            {metric.trend === 'up' ? (
                                                <TrendingUp className="h-4 w-4 text-success" />
                                            ) : (
                                                <TrendingDown className="h-4 w-4 text-destructive" />
                                            )}
                                            <span className={`text-sm font-medium ${
                                                metric.trend === 'up' ? 'text-success' : 'text-destructive'
                                            }`}>
                                                {metric.change}
                                            </span>
                                        </div>
                                    </div>
                                    <div className={`p-3 rounded-lg ${metric.color} text-white`}>
                                        <metric.icon className="h-6 w-6" />
                                    </div>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>

                {/* Charts Grid */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Attendance Trends */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold">Monthly Attendance Trends</h3>
                                <p className="text-sm text-muted-foreground">Attendance vs target over the past year</p>
                            </div>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart data={attendanceData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                        <XAxis dataKey="month" stroke="#6b7280" />
                                        <YAxis stroke="#6b7280" />
                                        <Tooltip 
                                            contentStyle={{ 
                                                backgroundColor: 'white', 
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px'
                                            }} 
                                        />
                                        <Bar dataKey="attendance" fill="#34b0e0" radius={[4, 4, 0, 0]} />
                                        <Bar dataKey="target" fill="#e5e7eb" radius={[4, 4, 0, 0]} />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </Card>

                    {/* Donation Trends */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold">Monthly Donations</h3>
                                <p className="text-sm text-muted-foreground">Financial contributions over time</p>
                            </div>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <AreaChart data={donationData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                        <XAxis dataKey="month" stroke="#6b7280" />
                                        <YAxis stroke="#6b7280" />
                                        <Tooltip 
                                            contentStyle={{ 
                                                backgroundColor: 'white', 
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px'
                                            }}
                                            formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']}
                                        />
                                        <Area 
                                            type="monotone" 
                                            dataKey="amount" 
                                            stroke="#4489e4" 
                                            fill="#4489e4" 
                                            fillOpacity={0.3}
                                        />
                                    </AreaChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Second Row Charts */}
                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Membership Growth */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold">Membership Growth</h3>
                                <p className="text-sm text-muted-foreground">New vs departing members</p>
                            </div>
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={membershipGrowthData}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                        <XAxis dataKey="month" stroke="#6b7280" />
                                        <YAxis stroke="#6b7280" />
                                        <Tooltip 
                                            contentStyle={{ 
                                                backgroundColor: 'white', 
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px'
                                            }} 
                                        />
                                        <Line type="monotone" dataKey="members" stroke="#34b0e0" strokeWidth={3} />
                                        <Line type="monotone" dataKey="newMembers" stroke="#22c55e" strokeWidth={2} />
                                        <Line type="monotone" dataKey="leftMembers" stroke="#ef4444" strokeWidth={2} />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </Card>

                    {/* Age Demographics */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold">Age Demographics</h3>
                                <p className="text-sm text-muted-foreground">Member distribution by age group</p>
                            </div>
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={ageGroupData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={40}
                                            outerRadius={80}
                                            paddingAngle={5}
                                            dataKey="value"
                                        >
                                            {ageGroupData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip 
                                            contentStyle={{ 
                                                backgroundColor: 'white', 
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px'
                                            }} 
                                        />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>
                            <div className="mt-4 space-y-2">
                                {ageGroupData.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between text-sm">
                                        <div className="flex items-center gap-2">
                                            <div 
                                                className="w-3 h-3 rounded-full" 
                                                style={{ backgroundColor: item.color }}
                                            />
                                            <span>{item.name}</span>
                                        </div>
                                        <span className="font-medium">{item.value}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>

                    {/* Service Attendance */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-6">
                                <h3 className="text-lg font-semibold">Service Attendance</h3>
                                <p className="text-sm text-muted-foreground">Attendance by service type</p>
                            </div>
                            <div className="space-y-4">
                                {serviceAttendanceData.map((service, index) => (
                                    <div key={index} className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="font-medium">{service.service}</span>
                                            <span className="text-muted-foreground">{service.attendance}</span>
                                        </div>
                                        <div className="w-full bg-muted rounded-full h-2">
                                            <div 
                                                className="bg-cyan h-2 rounded-full transition-all duration-500" 
                                                style={{ width: `${service.percentage}%` }}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}

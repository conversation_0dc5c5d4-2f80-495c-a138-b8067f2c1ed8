<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('devotionals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();
            $table->string('title');
            $table->string('slug');
            $table->text('content'); // Main devotional content
            $table->text('summary')->nullable(); // Short summary/excerpt
            $table->string('author')->nullable();
            $table->date('devotional_date')->nullable(); // For daily devotionals

            // Scripture and spiritual content
            $table->json('scripture_references')->nullable(); // Primary scripture references
            $table->text('key_verse')->nullable(); // Featured verse text
            $table->string('key_verse_reference')->nullable(); // Reference for key verse
            $table->text('prayer')->nullable(); // Closing prayer
            $table->text('reflection_questions')->nullable(); // Questions for reflection

            // Content organization
            $table->foreignId('category_id')->nullable()->constrained('content_categories')->onDelete('set null');
            $table->json('themes')->nullable(); // Array of themes/topics
            $table->string('series_name')->nullable(); // For devotional series
            $table->integer('series_order')->nullable();

            // Media attachments
            $table->string('featured_image_path')->nullable(); // Featured image
            $table->string('audio_path')->nullable(); // Audio version (optional)

            // Publishing and visibility
            $table->enum('status', ['draft', 'published', 'scheduled', 'archived'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->timestamp('scheduled_for')->nullable(); // For scheduled publishing
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Engagement tracking
            $table->integer('view_count')->default(0);
            $table->integer('share_count')->default(0);
            $table->json('tags')->nullable(); // Searchable tags

            $table->timestamps();

            // Composite unique constraint for slug + tenant_id
            $table->unique(['slug', 'tenant_id']);

            // Indexes for performance
            $table->index('tenant_id');
            $table->index(['status', 'published_at']);
            $table->index(['devotional_date', 'status']);
            $table->index(['category_id', 'status']);
            $table->index(['series_name', 'series_order']);
            $table->index('is_featured');
            $table->index('scheduled_for');
            $table->fullText(['title', 'content', 'summary']); // Full-text search
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('devotionals');
    }
};

<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContentCategoryRequest;
use App\Http\Requests\DevotionalRequest;
use App\Http\Requests\HymnRequest;
use App\Http\Requests\VideoRequest;
use App\Models\ContentCategory;
use App\Models\Devotional;
use App\Models\Hymn;
use App\Models\Video;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class ContentManagementController extends Controller
{
    /**
     * Display the main content management page.
     */
    public function index(): Response
    {
        $categories = ContentCategory::active()->ordered()->get();

        $videos = Video::with(['category', 'creator'])
            ->latest('created_at')
            ->take(10)
            ->get();

        $hymns = Hymn::with(['category', 'creator'])
            ->latest('created_at')
            ->take(10)
            ->get();

        $devotionals = Devotional::with(['category', 'creator'])
            ->latest('created_at')
            ->take(10)
            ->get();

        $stats = [
            'videos' => [
                'total' => Video::count(),
                'published' => Video::published()->count(),
                'draft' => Video::where('status', 'draft')->count(),
                'featured' => Video::where('is_featured', true)->count(),
            ],
            'hymns' => [
                'total' => Hymn::count(),
                'published' => Hymn::published()->count(),
                'draft' => Hymn::where('status', 'draft')->count(),
                'featured' => Hymn::where('is_featured', true)->count(),
            ],
            'devotionals' => [
                'total' => Devotional::count(),
                'published' => Devotional::published()->count(),
                'draft' => Devotional::where('status', 'draft')->count(),
                'scheduled' => Devotional::where('status', 'scheduled')->count(),
                'featured' => Devotional::where('is_featured', true)->count(),
            ],
            'categories' => [
                'total' => ContentCategory::count(),
                'active' => ContentCategory::active()->count(),
            ],
        ];

        return Inertia::render('content-management', [
            'categories' => $categories,
            'videos' => $videos,
            'hymns' => $hymns,
            'devotionals' => $devotionals,
            'stats' => $stats,
        ]);
    }

    // ===== CATEGORY MANAGEMENT =====

    /**
     * Store a new category.
     */
    public function storeCategory(ContentCategoryRequest $request): JsonResponse
    {
        try {
            $category = ContentCategory::create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully.',
                'category' => $category,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create category.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update an existing category.
     */
    public function updateCategory(ContentCategoryRequest $request, ContentCategory $category): JsonResponse
    {
        try {
            $category->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Category updated successfully.',
                'category' => $category->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update category.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a category.
     */
    public function destroyCategory(ContentCategory $category): JsonResponse
    {
        try {
            // Check if category has content
            $hasContent = $category->videos()->exists() ||
                $category->hymns()->exists() ||
                $category->devotionals()->exists();

            if ($hasContent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete category that contains content. Please move or delete the content first.',
                ], 422);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Category deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete category.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // ===== VIDEO MANAGEMENT =====

    /**
     * Get videos with filtering and pagination.
     */
    public function getVideos(Request $request): JsonResponse
    {
        $query = Video::with(['category', 'creator']);

        // Apply filters
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('speaker', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $videos = $query->paginate($request->get('per_page', 15));

        return response()->json($videos);
    }

    /**
     * Store a new video.
     */
    public function storeVideo(VideoRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            // Handle video file upload
            if ($request->hasFile('video_file')) {
                $videoFile = $request->file('video_file');
                $data['original_filename'] = $videoFile->getClientOriginalName();
                $data['mime_type'] = $videoFile->getMimeType();
                $data['file_size'] = $videoFile->getSize();
                $data['file_path'] = $videoFile->store('videos', 'public');
            }

            // Handle thumbnail upload
            if ($request->hasFile('thumbnail')) {
                $data['thumbnail_path'] = $request->file('thumbnail')->store('video-thumbnails', 'public');
            }

            $video = Video::create($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Video uploaded successfully.',
                'video' => $video->load(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload video.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update an existing video.
     */
    public function updateVideo(VideoRequest $request, Video $video): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            // Handle video file replacement
            if ($request->hasFile('video_file')) {
                // Delete old video file
                if ($video->file_path) {
                    Storage::disk('public')->delete($video->file_path);
                }

                $videoFile = $request->file('video_file');
                $data['original_filename'] = $videoFile->getClientOriginalName();
                $data['mime_type'] = $videoFile->getMimeType();
                $data['file_size'] = $videoFile->getSize();
                $data['file_path'] = $videoFile->store('videos', 'public');
            }

            // Handle thumbnail replacement
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail
                if ($video->thumbnail_path) {
                    Storage::disk('public')->delete($video->thumbnail_path);
                }

                $data['thumbnail_path'] = $request->file('thumbnail')->store('video-thumbnails', 'public');
            }

            $video->update($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Video updated successfully.',
                'video' => $video->fresh(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update video.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a video.
     */
    public function destroyVideo(Video $video): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete associated files
            if ($video->file_path) {
                Storage::disk('public')->delete($video->file_path);
            }

            if ($video->thumbnail_path) {
                Storage::disk('public')->delete($video->thumbnail_path);
            }

            $video->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Video deleted successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete video.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // ===== HYMN MANAGEMENT =====

    /**
     * Get hymns with filtering and pagination.
     */
    public function getHymns(Request $request): JsonResponse
    {
        $query = Hymn::with(['category', 'creator']);

        // Apply filters
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->search($search);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $hymns = $query->paginate($request->get('per_page', 15));

        return response()->json($hymns);
    }

    /**
     * Store a new hymn.
     */
    public function storeHymn(HymnRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            // Handle file uploads
            if ($request->hasFile('chord_chart')) {
                $data['chord_chart_path'] = $request->file('chord_chart')->store('hymn-charts', 'public');
            }

            if ($request->hasFile('sheet_music')) {
                $data['sheet_music_path'] = $request->file('sheet_music')->store('hymn-sheets', 'public');
            }

            if ($request->hasFile('audio_file')) {
                $data['audio_path'] = $request->file('audio_file')->store('hymn-audio', 'public');
            }

            $hymn = Hymn::create($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hymn created successfully.',
                'hymn' => $hymn->load(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create hymn.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update an existing hymn.
     */
    public function updateHymn(HymnRequest $request, Hymn $hymn): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            // Handle file replacements
            if ($request->hasFile('chord_chart')) {
                if ($hymn->chord_chart_path) {
                    Storage::disk('public')->delete($hymn->chord_chart_path);
                }
                $data['chord_chart_path'] = $request->file('chord_chart')->store('hymn-charts', 'public');
            }

            if ($request->hasFile('sheet_music')) {
                if ($hymn->sheet_music_path) {
                    Storage::disk('public')->delete($hymn->sheet_music_path);
                }
                $data['sheet_music_path'] = $request->file('sheet_music')->store('hymn-sheets', 'public');
            }

            if ($request->hasFile('audio_file')) {
                if ($hymn->audio_path) {
                    Storage::disk('public')->delete($hymn->audio_path);
                }
                $data['audio_path'] = $request->file('audio_file')->store('hymn-audio', 'public');
            }

            $hymn->update($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hymn updated successfully.',
                'hymn' => $hymn->fresh(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update hymn.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a hymn.
     */
    public function destroyHymn(Hymn $hymn): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete associated files
            if ($hymn->chord_chart_path) {
                Storage::disk('public')->delete($hymn->chord_chart_path);
            }

            if ($hymn->sheet_music_path) {
                Storage::disk('public')->delete($hymn->sheet_music_path);
            }

            if ($hymn->audio_path) {
                Storage::disk('public')->delete($hymn->audio_path);
            }

            $hymn->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Hymn deleted successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete hymn.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // ===== DEVOTIONAL MANAGEMENT =====

    /**
     * Get devotionals with filtering and pagination.
     */
    public function getDevotionals(Request $request): JsonResponse
    {
        $query = Devotional::with(['category', 'creator']);

        // Apply filters
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->search($search);
        }

        if ($request->filled('date_from')) {
            $query->where('devotional_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('devotional_date', '<=', $request->date_to);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $devotionals = $query->paginate($request->get('per_page', 15));

        return response()->json($devotionals);
    }

    /**
     * Store a new devotional.
     */
    public function storeDevotional(DevotionalRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();

            // Handle file uploads
            if ($request->hasFile('featured_image')) {
                $data['featured_image_path'] = $request->file('featured_image')->store('devotional-images', 'public');
            }

            if ($request->hasFile('audio_file')) {
                $data['audio_path'] = $request->file('audio_file')->store('devotional-audio', 'public');
            }

            $devotional = Devotional::create($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Devotional created successfully.',
                'devotional' => $devotional->load(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create devotional.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update an existing devotional.
     */
    public function updateDevotional(DevotionalRequest $request, Devotional $devotional): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();
            $data['updated_by'] = Auth::id();

            // Handle file replacements
            if ($request->hasFile('featured_image')) {
                if ($devotional->featured_image_path) {
                    Storage::disk('public')->delete($devotional->featured_image_path);
                }
                $data['featured_image_path'] = $request->file('featured_image')->store('devotional-images', 'public');
            }

            if ($request->hasFile('audio_file')) {
                if ($devotional->audio_path) {
                    Storage::disk('public')->delete($devotional->audio_path);
                }
                $data['audio_path'] = $request->file('audio_file')->store('devotional-audio', 'public');
            }

            $devotional->update($data);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Devotional updated successfully.',
                'devotional' => $devotional->fresh(['category', 'creator']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to update devotional.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a devotional.
     */
    public function destroyDevotional(Devotional $devotional): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete associated files
            if ($devotional->featured_image_path) {
                Storage::disk('public')->delete($devotional->featured_image_path);
            }

            if ($devotional->audio_path) {
                Storage::disk('public')->delete($devotional->audio_path);
            }

            $devotional->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Devotional deleted successfully.',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete devotional.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // ===== UTILITY METHODS =====

    /**
     * Get content statistics.
     */
    public function getStats(): JsonResponse
    {
        $stats = [
            'videos' => [
                'total' => Video::count(),
                'published' => Video::published()->count(),
                'draft' => Video::where('status', 'draft')->count(),
                'featured' => Video::featured()->count(),
            ],
            'hymns' => [
                'total' => Hymn::count(),
                'published' => Hymn::published()->count(),
                'draft' => Hymn::where('status', 'draft')->count(),
                'featured' => Hymn::featured()->count(),
            ],
            'devotionals' => [
                'total' => Devotional::count(),
                'published' => Devotional::published()->count(),
                'draft' => Devotional::where('status', 'draft')->count(),
                'scheduled' => Devotional::scheduled()->count(),
                'featured' => Devotional::featured()->count(),
            ],
            'categories' => [
                'total' => ContentCategory::count(),
                'active' => ContentCategory::active()->count(),
            ],
        ];

        return response()->json($stats);
    }

    /**
     * Search across all content types.
     */
    public function search(Request $request): JsonResponse
    {
        $search = $request->get('q', '');
        $limit = $request->get('limit', 10);

        if (empty($search)) {
            return response()->json([
                'videos' => [],
                'hymns' => [],
                'devotionals' => [],
            ]);
        }

        $videos = Video::where('title', 'like', "%{$search}%")
            ->orWhere('description', 'like', "%{$search}%")
            ->orWhere('speaker', 'like', "%{$search}%")
            ->with('category')
            ->limit($limit)
            ->get();

        $hymns = Hymn::search($search)
            ->with('category')
            ->limit($limit)
            ->get();

        $devotionals = Devotional::search($search)
            ->with('category')
            ->limit($limit)
            ->get();

        return response()->json([
            'videos' => $videos,
            'hymns' => $hymns,
            'devotionals' => $devotionals,
        ]);
    }
}

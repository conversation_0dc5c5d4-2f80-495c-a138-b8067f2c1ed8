import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Icon } from '@/components/ui/icon';
import { type UpcomingEvent } from '@/data/church-dashboard-data';
import { Calendar, Clock, MapPin, Users } from 'lucide-react';

interface UpcomingEventsProps {
    events: UpcomingEvent[];
    showAll?: boolean;
}

const eventTypeColors = {
    service: 'bg-primary/10 text-primary border-primary/20',
    meeting: 'bg-info/10 text-info border-info/20',
    event: 'bg-success/10 text-success border-success/20',
    study: 'bg-warning/10 text-warning border-warning/20',
};

const eventTypeLabels = {
    service: 'Service',
    meeting: 'Meeting',
    event: 'Event',
    study: 'Bible Study',
};

export function UpcomingEvents({ events, showAll = false }: UpcomingEventsProps) {
    const displayedEvents = showAll ? events : events.slice(0, 5);

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        } else {
            return date.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric',
            });
        }
    };

    return (
        <Card className="shadow-sm transition-all duration-200 hover:shadow-md">
            <div className="p-5">
                <div className="mb-5 flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-foreground">Upcoming Events</h3>
                    <Button variant="outline" size="sm" className="h-8 border-muted-foreground/20 text-xs hover:bg-muted/50">
                        View All
                    </Button>
                </div>

                <div className="space-y-3">
                    {displayedEvents.map((event) => (
                        <div
                            key={event.id}
                            className="flex items-start gap-4 rounded border border-border/50 p-3 transition-all duration-200 hover:border-border hover:bg-muted/30"
                        >
                            <div className="min-w-0 flex-1 space-y-2">
                                <div className="flex items-start justify-between gap-2">
                                    <div className="min-w-0 flex-1">
                                        <h4 className="truncate font-medium text-foreground">{event.title}</h4>
                                        <div className="mt-1 flex flex-wrap items-center gap-2 text-sm text-muted-foreground sm:gap-4">
                                            <div className="flex flex-shrink-0 items-center gap-1">
                                                <Icon iconNode={Calendar} className="h-3 w-3" />
                                                <span className="truncate">{formatDate(event.date)}</span>
                                            </div>
                                            <div className="flex flex-shrink-0 items-center gap-1">
                                                <Icon iconNode={Clock} className="h-3 w-3" />
                                                <span className="truncate">{event.time}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <Badge variant="outline" className={`${eventTypeColors[event.type]} flex-shrink-0 text-xs`}>
                                        {eventTypeLabels[event.type]}
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between gap-2 text-sm">
                                    <div className="flex min-w-0 flex-1 items-center gap-1 text-muted-foreground">
                                        <Icon iconNode={MapPin} className="h-3 w-3 flex-shrink-0" />
                                        <span className="truncate">{event.location}</span>
                                    </div>
                                    {event.attendees && (
                                        <div className="flex flex-shrink-0 items-center gap-1 text-muted-foreground">
                                            <Icon iconNode={Users} className="h-3 w-3" />
                                            <span className="text-xs">{event.attendees}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {!showAll && events.length > 5 && (
                    <div className="mt-4 text-center">
                        <Button variant="ghost" size="sm" className="text-xs text-muted-foreground hover:text-foreground">
                            Show {events.length - 5} more events
                        </Button>
                    </div>
                )}
            </div>
        </Card>
    );
}

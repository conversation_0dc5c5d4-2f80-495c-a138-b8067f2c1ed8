<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VideoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $videoId = $this->route('video')?->id;
        $isUpdate = $this->isMethod('PUT') || $this->isMethod('PATCH');

        return [
            'title' => ['required', 'string', 'max:255'],
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('videos', 'slug')->ignore($videoId),
            ],
            'description' => ['nullable', 'string', 'max:2000'],
            'speaker' => ['nullable', 'string', 'max:255'],
            'recorded_date' => ['nullable', 'date', 'before_or_equal:today'],
            'scripture_references' => ['nullable', 'array'],
            'scripture_references.*' => ['string', 'max:100'],
            'type' => ['required', 'in:sermon,testimony,announcement,event,other'],

            // File upload validation
            'video_file' => [
                $isUpdate ? 'nullable' : 'required',
                'file',
                'mimes:mp4,avi,mov,wmv,flv,webm,mkv',
                'max:2097152', // 2GB in KB
            ],
            'thumbnail' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:10240', // 10MB in KB
            ],

            // Content organization
            'category_id' => ['nullable', 'exists:content_categories,id'],
            'series_name' => ['nullable', 'string', 'max:255'],
            'series_order' => ['nullable', 'integer', 'min:1'],

            // Publishing
            'status' => ['required', 'in:draft,published,archived'],
            'is_featured' => ['boolean'],
            'published_at' => ['nullable', 'date', 'after_or_equal:today'],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Video title is required.',
            'title.max' => 'Video title cannot exceed 255 characters.',
            'slug.unique' => 'This slug is already taken. Please choose a different one.',
            'slug.regex' => 'Slug can only contain lowercase letters, numbers, and hyphens.',
            'description.max' => 'Description cannot exceed 2000 characters.',
            'speaker.max' => 'Speaker name cannot exceed 255 characters.',
            'recorded_date.before_or_equal' => 'Recorded date cannot be in the future.',
            'type.required' => 'Video type is required.',
            'type.in' => 'Invalid video type selected.',

            // File validation messages
            'video_file.required' => 'Video file is required.',
            'video_file.file' => 'Please upload a valid video file.',
            'video_file.mimes' => 'Video must be in one of these formats: MP4, AVI, MOV, WMV, FLV, WebM, MKV.',
            'video_file.max' => 'Video file size cannot exceed 2GB.',
            'thumbnail.image' => 'Thumbnail must be an image file.',
            'thumbnail.mimes' => 'Thumbnail must be in one of these formats: JPEG, PNG, JPG, GIF, WebP.',
            'thumbnail.max' => 'Thumbnail file size cannot exceed 10MB.',

            // Organization messages
            'category_id.exists' => 'Selected category does not exist.',
            'series_name.max' => 'Series name cannot exceed 255 characters.',
            'series_order.min' => 'Series order must be at least 1.',

            // Publishing messages
            'status.required' => 'Video status is required.',
            'status.in' => 'Invalid video status selected.',
            'published_at.after_or_equal' => 'Published date cannot be in the past.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'video_file' => 'video file',
            'is_featured' => 'featured status',
            'published_at' => 'publish date',
            'category_id' => 'category',
            'series_order' => 'series order',
        ];
    }
}

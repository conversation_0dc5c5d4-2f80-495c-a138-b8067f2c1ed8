<?php

use App\Http\Controllers\ChurchRegistrationController;
use App\Http\Controllers\ChurchSettingsController;
use App\Http\Controllers\ContentManagementController;
use App\Http\Controllers\NotificationsController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SuperAdminController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Church Registration Routes (public, no tenant middleware)
Route::middleware('guest')->group(function () {
    Route::get('/church-registration', [ChurchRegistrationController::class, 'create'])
        ->name('church-registration.create');
    Route::post('/church-registration', [ChurchRegistrationController::class, 'store'])
        ->name('church-registration.store');
    Route::post('/church-registration/check-slug', [ChurchRegistrationController::class, 'checkSlug'])
        ->name('church-registration.check-slug');
});

Route::middleware(['auth', 'verified', 'tenant'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('theme-showcase', function () {
        return Inertia::render('theme-showcase');
    })->name('theme-showcase');

    Route::get('members', function () {
        return Inertia::render('members');
    })->name('members');

    Route::get('admin/analytics', function () {
        return Inertia::render('admin/analytics');
    })->name('admin.analytics');

    Route::get('live-attendance', function () {
        return Inertia::render('live-attendance');
    })->name('live-attendance');

    Route::get('manual-checkin', function () {
        return Inertia::render('manual-checkin');
    })->name('manual-checkin');

    // Church Settings
    Route::get('church-settings', [ChurchSettingsController::class, 'index'])->name('church-settings');
    Route::put('church-settings', [ChurchSettingsController::class, 'update'])->name('church-settings.update');

    // Notifications Management
    Route::get('notifications', [NotificationsController::class, 'index'])->name('notifications');
    Route::post('notifications/send', [NotificationsController::class, 'send'])->name('notifications.send');
    Route::post('notifications/schedule', [NotificationsController::class, 'schedule'])->name('notifications.schedule');
    Route::get('notifications/history', [NotificationsController::class, 'history'])->name('notifications.history');

    // Content Management System
    Route::get('content-management', [ContentManagementController::class, 'index'])->name('content-management');

    // Category Management
    Route::post('content-management/categories', [ContentManagementController::class, 'storeCategory'])->name('content-management.categories.store');
    Route::put('content-management/categories/{category}', [ContentManagementController::class, 'updateCategory'])->name('content-management.categories.update');
    Route::delete('content-management/categories/{category}', [ContentManagementController::class, 'destroyCategory'])->name('content-management.categories.destroy');

    // Video Management
    Route::get('content-management/videos', [ContentManagementController::class, 'getVideos'])->name('content-management.videos.index');
    Route::post('content-management/videos', [ContentManagementController::class, 'storeVideo'])->name('content-management.videos.store');
    Route::put('content-management/videos/{video}', [ContentManagementController::class, 'updateVideo'])->name('content-management.videos.update');
    Route::delete('content-management/videos/{video}', [ContentManagementController::class, 'destroyVideo'])->name('content-management.videos.destroy');

    // Hymn Management
    Route::get('content-management/hymns', [ContentManagementController::class, 'getHymns'])->name('content-management.hymns.index');
    Route::post('content-management/hymns', [ContentManagementController::class, 'storeHymn'])->name('content-management.hymns.store');
    Route::put('content-management/hymns/{hymn}', [ContentManagementController::class, 'updateHymn'])->name('content-management.hymns.update');
    Route::delete('content-management/hymns/{hymn}', [ContentManagementController::class, 'destroyHymn'])->name('content-management.hymns.destroy');

    // Devotional Management
    Route::get('content-management/devotionals', [ContentManagementController::class, 'getDevotionals'])->name('content-management.devotionals.index');
    Route::post('content-management/devotionals', [ContentManagementController::class, 'storeDevotional'])->name('content-management.devotionals.store');
    Route::put('content-management/devotionals/{devotional}', [ContentManagementController::class, 'updateDevotional'])->name('content-management.devotionals.update');
    Route::delete('content-management/devotionals/{devotional}', [ContentManagementController::class, 'destroyDevotional'])->name('content-management.devotionals.destroy');

    // Utility Routes
    Route::get('content-management/stats', [ContentManagementController::class, 'getStats'])->name('content-management.stats');
    Route::get('content-management/search', [ContentManagementController::class, 'search'])->name('content-management.search');

    // Reports System
    Route::get('reports', [ReportsController::class, 'index'])->name('reports');
    Route::get('reports/weekly-attendance', [ReportsController::class, 'weeklyAttendance'])->name('reports.weekly-attendance');
    Route::get('reports/monthly-trends', [ReportsController::class, 'monthlyTrends'])->name('reports.monthly-trends');
    Route::get('reports/service-attendance', [ReportsController::class, 'serviceAttendance'])->name('reports.service-attendance');
    Route::get('reports/member-attendance', [ReportsController::class, 'memberAttendance'])->name('reports.member-attendance');
    Route::get('reports/attendance-comparison', [ReportsController::class, 'attendanceComparison'])->name('reports.attendance-comparison');
    Route::post('reports/export', [ReportsController::class, 'exportReport'])->name('reports.export');
});

// Super Admin Routes (no tenant middleware, requires super admin)
Route::middleware(['auth', 'super-admin'])->prefix('super-admin')->name('super-admin.')->group(function () {
    Route::get('/dashboard', [SuperAdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/churches', [SuperAdminController::class, 'churches'])->name('churches');
    Route::get('/churches/{church}', [SuperAdminController::class, 'showChurch'])->name('churches.show');
    Route::patch('/churches/{church}/status', [SuperAdminController::class, 'updateChurchStatus'])->name('churches.update-status');
    Route::post('/churches/{church}/switch', [SuperAdminController::class, 'switchToChurch'])->name('churches.switch');
    Route::get('/administration', function () {
        return Inertia::render('Churches/Administration');
    })->name('administration');
    Route::post('/return', [SuperAdminController::class, 'returnToSuperAdmin'])->name('return');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';

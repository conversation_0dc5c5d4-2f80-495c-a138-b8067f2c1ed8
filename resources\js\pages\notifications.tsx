import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Calendar, CheckCircle, Clock, Eye, Mail, MessageSquare, Send, Smartphone, Users, Zap } from 'lucide-react';
import { useState } from 'react';

interface NotificationTemplate {
    subject?: string;
    message: string;
}

interface NotificationsPageProps {
    members: Array<{
        id: number;
        name: string;
        email: string;
        phone?: string;
        group?: string;
    }>;
    templates: Record<string, NotificationTemplate>;
    recentNotifications: Array<{
        id: number;
        type: string;
        subject?: string;
        message: string;
        recipients_count: number;
        sent_at: string;
        status: 'sent' | 'scheduled' | 'failed';
    }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Notifications', href: '/notifications' },
];

const notificationTypes = [
    { value: 'sms', label: 'SMS Message', icon: MessageSquare, description: 'Send SMS to member phones' },
    { value: 'email', label: 'Email Campaign', icon: Mail, description: 'Send email announcements' },
    { value: 'push', label: 'Push Notification', icon: Smartphone, description: 'Mobile app notifications' },
    { value: 'announcement', label: 'General Announcement', icon: Zap, description: 'Church-wide announcements' },
];

export default function NotificationsPage({ members = [], templates = {}, recentNotifications = [] }: NotificationsPageProps) {
    const [selectedType, setSelectedType] = useState('email');
    const [selectedRecipients, setSelectedRecipients] = useState<number[]>([]);
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [selectedTemplate, setSelectedTemplate] = useState('');
    const [scheduledDate, setScheduledDate] = useState('');
    const [scheduledTime, setScheduledTime] = useState('');
    const [previewOpen, setPreviewOpen] = useState(false);
    const [selectAll, setSelectAll] = useState(false);

    // Mock members data if empty
    const mockMembers =
        members.length === 0
            ? [
                  { id: 1, name: 'John Smith', email: '<EMAIL>', phone: '+1234567890', group: 'Adult Ministry' },
                  { id: 2, name: 'Sarah Johnson', email: '<EMAIL>', phone: '+1234567891', group: 'Youth Ministry' },
                  { id: 3, name: 'Michael Brown', email: '<EMAIL>', phone: '+1234567892', group: 'Children Ministry' },
                  { id: 4, name: 'Emily Davis', email: '<EMAIL>', phone: '+1234567893', group: 'Adult Ministry' },
                  { id: 5, name: 'David Wilson', email: '<EMAIL>', phone: '+1234567894', group: 'Senior Ministry' },
              ]
            : members;

    const mockTemplates =
        Object.keys(templates).length === 0
            ? {
                  welcome: {
                      subject: 'Welcome to our Church Community',
                      message: "Welcome to our church family! We're excited to have you join us in worship and fellowship.",
                  },
                  birthday: { subject: 'Happy Birthday!', message: "Wishing you a blessed birthday filled with joy, love, and God's grace." },
                  service_reminder: { subject: 'Service Reminder', message: "Don't forget about our upcoming service this Sunday. See you there!" },
                  event_announcement: {
                      subject: 'Church Event Announcement',
                      message: 'We have an exciting event coming up! Join us for fellowship and fun.',
                  },
              }
            : templates;

    const handleRecipientToggle = (memberId: number) => {
        setSelectedRecipients((prev) => (prev.includes(memberId) ? prev.filter((id) => id !== memberId) : [...prev, memberId]));
    };

    const handleSelectAll = () => {
        if (selectAll) {
            setSelectedRecipients([]);
        } else {
            setSelectedRecipients(mockMembers.map((member) => member.id));
        }
        setSelectAll(!selectAll);
    };

    const handleTemplateSelect = (templateKey: string) => {
        if (templateKey && mockTemplates[templateKey]) {
            const template = mockTemplates[templateKey];
            setSubject(template.subject || '');
            setMessage(template.message);
            setSelectedTemplate(templateKey);
        }
    };

    const handleSendNotification = () => {
        const data = {
            type: selectedType,
            recipients: selectedRecipients,
            subject: selectedType === 'email' ? subject : undefined,
            message: message,
            scheduled_at: scheduledDate && scheduledTime ? `${scheduledDate} ${scheduledTime}` : undefined,
        };

        router.post('/notifications/send', data, {
            onSuccess: () => {
                // Reset form
                setSelectedRecipients([]);
                setSubject('');
                setMessage('');
                setSelectedTemplate('');
                setScheduledDate('');
                setScheduledTime('');
            },
        });
    };

    const selectedTypeInfo = notificationTypes.find((type) => type.value === selectedType);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Notifications Management" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold tracking-tight">Notifications Management</h1>
                    <p className="text-muted-foreground">Send SMS, emails, and push notifications to your church members</p>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        <Tabs value={selectedType} onValueChange={setSelectedType} className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                                {notificationTypes.map((type) => {
                                    const Icon = type.icon;
                                    return (
                                        <TabsTrigger key={type.value} value={type.value} className="flex items-center gap-2">
                                            <Icon className="size-4" />
                                            <span className="hidden sm:inline">{type.label.split(' ')[0]}</span>
                                        </TabsTrigger>
                                    );
                                })}
                            </TabsList>

                            {notificationTypes.map((type) => (
                                <TabsContent key={type.value} value={type.value} className="space-y-4">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <type.icon className="size-5 text-primary" />
                                                {type.label}
                                            </CardTitle>
                                            <CardDescription>{type.description}</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            {/* Template Selection */}
                                            <div className="space-y-2">
                                                <Label htmlFor="template">Quick Templates</Label>
                                                <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Choose a template or create custom message" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="custom">Custom Message</SelectItem>
                                                        {Object.entries(mockTemplates).map(([key, template]) => (
                                                            <SelectItem key={key} value={key}>
                                                                {template.subject || key.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            {/* Subject (for email) */}
                                            {selectedType === 'email' && (
                                                <div className="space-y-2">
                                                    <Label htmlFor="subject">Subject</Label>
                                                    <Input
                                                        id="subject"
                                                        value={subject}
                                                        onChange={(e) => setSubject(e.target.value)}
                                                        placeholder="Enter email subject"
                                                    />
                                                </div>
                                            )}

                                            {/* Message */}
                                            <div className="space-y-2">
                                                <Label htmlFor="message">Message</Label>
                                                <Textarea
                                                    id="message"
                                                    value={message}
                                                    onChange={(e) => setMessage(e.target.value)}
                                                    placeholder={`Enter your ${selectedType} message...`}
                                                    className="min-h-[120px]"
                                                />
                                                <div className="text-xs text-muted-foreground">
                                                    {message.length} characters
                                                    {selectedType === 'sms' && message.length > 160 && (
                                                        <span className="ml-2 text-yellow-600">Multiple SMS messages may be sent</span>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Scheduling */}
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="date">Schedule Date (Optional)</Label>
                                                    <Input
                                                        id="date"
                                                        type="date"
                                                        value={scheduledDate}
                                                        onChange={(e) => setScheduledDate(e.target.value)}
                                                        min={new Date().toISOString().split('T')[0]}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="time">Schedule Time</Label>
                                                    <Input
                                                        id="time"
                                                        type="time"
                                                        value={scheduledTime}
                                                        onChange={(e) => setScheduledTime(e.target.value)}
                                                        disabled={!scheduledDate}
                                                    />
                                                </div>
                                            </div>

                                            {/* Action Buttons */}
                                            <div className="flex gap-3 pt-4">
                                                <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
                                                    <DialogTrigger asChild>
                                                        <Button variant="outline" className="flex items-center gap-2">
                                                            <Eye className="size-4" />
                                                            Preview
                                                        </Button>
                                                    </DialogTrigger>
                                                    <DialogContent>
                                                        <DialogHeader>
                                                            <DialogTitle>Preview {selectedTypeInfo?.label}</DialogTitle>
                                                            <DialogDescription>
                                                                This is how your {selectedType} will appear to recipients
                                                            </DialogDescription>
                                                        </DialogHeader>
                                                        <div className="space-y-4">
                                                            {selectedType === 'email' && subject && (
                                                                <div>
                                                                    <Label className="font-semibold">Subject:</Label>
                                                                    <p className="rounded bg-muted p-2 text-sm">{subject}</p>
                                                                </div>
                                                            )}
                                                            <div>
                                                                <Label className="font-semibold">Message:</Label>
                                                                <div className="rounded bg-muted p-4 text-sm whitespace-pre-wrap">
                                                                    {message || 'No message content'}
                                                                </div>
                                                            </div>
                                                            <div className="text-xs text-muted-foreground">
                                                                Recipients: {selectedRecipients.length} members
                                                            </div>
                                                        </div>
                                                    </DialogContent>
                                                </Dialog>

                                                <Button
                                                    onClick={handleSendNotification}
                                                    disabled={!message || selectedRecipients.length === 0}
                                                    className="flex items-center gap-2"
                                                >
                                                    {scheduledDate ? (
                                                        <>
                                                            <Clock className="size-4" />
                                                            Schedule
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Send className="size-4" />
                                                            Send Now
                                                        </>
                                                    )}
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            ))}
                        </Tabs>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Recipients Selection */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="size-5" />
                                    Recipients ({selectedRecipients.length})
                                </CardTitle>
                                <CardDescription>Select members to send notifications</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="select-all" checked={selectAll} onCheckedChange={handleSelectAll} />
                                    <Label htmlFor="select-all" className="font-medium">
                                        Select All Members
                                    </Label>
                                </div>
                                <Separator />
                                <div className="max-h-[300px] space-y-2 overflow-y-auto">
                                    {mockMembers.map((member) => (
                                        <div key={member.id} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={`member-${member.id}`}
                                                checked={selectedRecipients.includes(member.id)}
                                                onCheckedChange={() => handleRecipientToggle(member.id)}
                                            />
                                            <div className="min-w-0 flex-1">
                                                <Label htmlFor={`member-${member.id}`} className="block cursor-pointer text-sm font-medium">
                                                    {member.name}
                                                </Label>
                                                <p className="truncate text-xs text-muted-foreground">{member.group}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Notification Stats</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Total Members</span>
                                    <Badge variant="secondary">{mockMembers.length}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">Selected</span>
                                    <Badge variant="default">{selectedRecipients.length}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-muted-foreground">This Month</span>
                                    <Badge variant="outline">12 sent</Badge>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Recent Notifications */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="size-5" />
                                    Recent Activity
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {/* Mock recent notifications */}
                                    {[
                                        { type: 'email', subject: 'Sunday Service Reminder', recipients: 45, time: '2 hours ago', status: 'sent' },
                                        { type: 'sms', subject: 'Prayer Meeting Tonight', recipients: 23, time: '1 day ago', status: 'sent' },
                                        { type: 'push', subject: 'Bible Study Canceled', recipients: 67, time: '3 days ago', status: 'sent' },
                                    ].map((notification, index) => (
                                        <div key={index} className="flex items-start space-x-3 rounded-lg bg-muted/50 p-3">
                                            <div className="flex-shrink-0">
                                                {notification.type === 'email' && <Mail className="size-4 text-blue" />}
                                                {notification.type === 'sms' && <MessageSquare className="size-4 text-green" />}
                                                {notification.type === 'push' && <Smartphone className="size-4 text-purple" />}
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="truncate text-sm font-medium">{notification.subject}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {notification.recipients} recipients • {notification.time}
                                                </p>
                                            </div>
                                            <CheckCircle className="size-4 flex-shrink-0 text-green" />
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Bottom Alert */}
                {selectedRecipients.length > 0 && (
                    <Alert>
                        <Zap className="size-4" />
                        <AlertTitle>Ready to send!</AlertTitle>
                        <AlertDescription>
                            You have selected {selectedRecipients.length} recipients for your {selectedTypeInfo?.label.toLowerCase()}.
                            {scheduledDate && scheduledTime
                                ? ` Scheduled for ${scheduledDate} at ${scheduledTime}.`
                                : ' Click "Send Now" to deliver immediately.'}
                        </AlertDescription>
                    </Alert>
                )}
            </div>
        </AppLayout>
    );
}

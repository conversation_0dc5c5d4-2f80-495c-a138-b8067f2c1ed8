<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ChurchMembersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $members = [
            // Leadership
            ['name' => 'Pastor <PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            
            // Regular Members
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>', 'email' => 'mary.rod<PERSON>ue<PERSON>@church.com'],
            ['name' => '<PERSON>', 'email' => '<EMAIL>'],
            ['name' => 'Patricia White', 'email' => '<EMAIL>'],
            ['name' => 'Christopher Harris', 'email' => '<EMAIL>'],
            ['name' => 'Linda Clark', 'email' => '<EMAIL>'],
            ['name' => 'Daniel Lewis', 'email' => '<EMAIL>'],
            ['name' => 'Barbara Walker', 'email' => '<EMAIL>'],
            ['name' => 'Matthew Hall', 'email' => '<EMAIL>'],
            ['name' => 'Susan Allen', 'email' => '<EMAIL>'],
            ['name' => 'Anthony Young', 'email' => '<EMAIL>'],
            ['name' => 'Karen King', 'email' => '<EMAIL>'],
            
            // Youth Members
            ['name' => 'Joshua Wright', 'email' => '<EMAIL>'],
            ['name' => 'Ashley Lopez', 'email' => '<EMAIL>'],
            ['name' => 'Andrew Hill', 'email' => '<EMAIL>'],
            ['name' => 'Megan Scott', 'email' => '<EMAIL>'],
            ['name' => 'Ryan Green', 'email' => '<EMAIL>'],
            ['name' => 'Hannah Adams', 'email' => '<EMAIL>'],
            ['name' => 'Tyler Baker', 'email' => '<EMAIL>'],
            ['name' => 'Samantha Nelson', 'email' => '<EMAIL>'],
            ['name' => 'Brandon Carter', 'email' => '<EMAIL>'],
            ['name' => 'Alexis Mitchell', 'email' => '<EMAIL>'],
            
            // Families
            ['name' => 'Mark Thompson', 'email' => '<EMAIL>'],
            ['name' => 'Carol Thompson', 'email' => '<EMAIL>'],
            ['name' => 'Steve Roberts', 'email' => '<EMAIL>'],
            ['name' => 'Nancy Roberts', 'email' => '<EMAIL>'],
            ['name' => 'Paul Turner', 'email' => '<EMAIL>'],
            ['name' => 'Helen Turner', 'email' => '<EMAIL>'],
            ['name' => 'Kevin Phillips', 'email' => '<EMAIL>'],
            ['name' => 'Michelle Phillips', 'email' => '<EMAIL>'],
            ['name' => 'Brian Campbell', 'email' => '<EMAIL>'],
            ['name' => 'Donna Campbell', 'email' => '<EMAIL>'],
            
            // Senior Members
            ['name' => 'George Parker', 'email' => '<EMAIL>'],
            ['name' => 'Dorothy Parker', 'email' => '<EMAIL>'],
            ['name' => 'Frank Evans', 'email' => '<EMAIL>'],
            ['name' => 'Ruth Evans', 'email' => '<EMAIL>'],
            ['name' => 'Harold Edwards', 'email' => '<EMAIL>'],
            ['name' => 'Betty Edwards', 'email' => '<EMAIL>'],
            ['name' => 'Walter Collins', 'email' => '<EMAIL>'],
            ['name' => 'Joyce Collins', 'email' => '<EMAIL>'],
            ['name' => 'Arthur Stewart', 'email' => '<EMAIL>'],
            ['name' => 'Frances Stewart', 'email' => '<EMAIL>'],
            
            // New Members
            ['name' => 'Jacob Morris', 'email' => '<EMAIL>'],
            ['name' => 'Emma Rogers', 'email' => '<EMAIL>'],
            ['name' => 'Noah Reed', 'email' => '<EMAIL>'],
            ['name' => 'Olivia Cook', 'email' => '<EMAIL>'],
            ['name' => 'Liam Bailey', 'email' => '<EMAIL>'],
            ['name' => 'Sophia Rivera', 'email' => '<EMAIL>'],
            ['name' => 'Mason Cooper', 'email' => '<EMAIL>'],
            ['name' => 'Isabella Richardson', 'email' => '<EMAIL>'],
            ['name' => 'Ethan Cox', 'email' => '<EMAIL>'],
            ['name' => 'Ava Ward', 'email' => '<EMAIL>'],
            
            // Occasional Attendees
            ['name' => 'Lucas Torres', 'email' => '<EMAIL>'],
            ['name' => 'Chloe Peterson', 'email' => '<EMAIL>'],
            ['name' => 'Owen Gray', 'email' => '<EMAIL>'],
            ['name' => 'Grace Ramirez', 'email' => '<EMAIL>'],
            ['name' => 'Carter James', 'email' => '<EMAIL>'],
            ['name' => 'Lily Watson', 'email' => '<EMAIL>'],
            ['name' => 'Caleb Brooks', 'email' => '<EMAIL>'],
            ['name' => 'Zoe Kelly', 'email' => '<EMAIL>'],
            ['name' => 'Hunter Sanders', 'email' => '<EMAIL>'],
            ['name' => 'Natalie Price', 'email' => '<EMAIL>'],
            
            // Volunteers
            ['name' => 'Ian Bennett', 'email' => '<EMAIL>'],
            ['name' => 'Maya Wood', 'email' => '<EMAIL>'],
            ['name' => 'Gavin Barnes', 'email' => '<EMAIL>'],
            ['name' => 'Leah Ross', 'email' => '<EMAIL>'],
            ['name' => 'Wyatt Henderson', 'email' => '<EMAIL>'],
            ['name' => 'Addison Coleman', 'email' => '<EMAIL>'],
            ['name' => 'Nolan Jenkins', 'email' => '<EMAIL>'],
            ['name' => 'Haley Perry', 'email' => '<EMAIL>'],
            ['name' => 'Aaron Powell', 'email' => '<EMAIL>'],
            ['name' => 'Kaylee Long', 'email' => '<EMAIL>'],
        ];

        foreach ($members as $member) {
            User::create([
                'name' => $member['name'],
                'email' => $member['email'],
                'password' => Hash::make('password'), // Default password for testing
                'email_verified_at' => now(),
            ]);
        }
    }
}

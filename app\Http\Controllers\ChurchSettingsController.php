<?php

namespace App\Http\Controllers;

use App\Http\Requests\ChurchSettingsRequest;
use App\Models\ChurchSetting;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ChurchSettingsController extends Controller
{
    public function index(): Response
    {
        $settings = ChurchSetting::current();

        return Inertia::render('church-settings', [
            'settings' => $settings,
        ]);
    }

    public function update(ChurchSettingsRequest $request): RedirectResponse
    {
        $settings = ChurchSetting::current();

        $settings->update($request->validated());

        return redirect()
            ->back()
            ->with('success', 'Church settings updated successfully.');
    }
}

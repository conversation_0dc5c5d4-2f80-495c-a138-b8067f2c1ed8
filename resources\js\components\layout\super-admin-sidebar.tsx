import { NavMain } from '@/components/layout/nav-main';
import { NavUser } from '@/components/layout/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { LayoutGrid, Building2, Users, Settings, Shield, BarChart3, Database, Globe } from 'lucide-react';
import AppLogo from './app-logo';

// Flat navigation structure as per user preference - no nested dropdowns
const superAdminNavItems: NavItem[] = [
    {
        title: 'Super Admin Dashboard',
        href: '/super-admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Tenant Management',
        href: '/super-admin/tenants',
        icon: Building2,
    },
    {
        title: 'System Analytics',
        href: '/super-admin/analytics',
        icon: BarChart3,
    },
    {
        title: 'User Management',
        href: '/super-admin/users',
        icon: Users,
    },
    {
        title: 'System Settings',
        href: '/super-admin/settings',
        icon: Settings,
    },
    {
        title: 'Security & Permissions',
        href: '/super-admin/security',
        icon: Shield,
    },
    {
        title: 'Database Management',
        href: '/super-admin/database',
        icon: Database,
    },
    {
        title: 'Global Configuration',
        href: '/super-admin/config',
        icon: Globe,
    },
];

export function SuperAdminSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="border-r-0">
            <SidebarHeader className="border-b border-sidebar-border/50 bg-sidebar">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild className="hover:bg-sidebar-accent/50">
                            <Link href="/super-admin/dashboard" prefetch>
                                <AppLogo />
                                <div className="flex flex-col gap-0.5 leading-none">
                                    <span className="font-semibold">Flockin</span>
                                    <span className="text-xs text-muted-foreground">Super Admin</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="bg-sidebar">
                <NavMain items={superAdminNavItems} />
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border/50 bg-sidebar">
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}

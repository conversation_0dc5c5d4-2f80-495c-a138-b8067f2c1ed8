<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->date('service_date');
            $table->timestamp('check_in_time')->nullable();
            $table->timestamp('check_out_time')->nullable();
            $table->enum('status', ['present', 'late', 'absent'])->default('present');
            $table->decimal('location_lat', 10, 8)->nullable();
            $table->decimal('location_lng', 11, 8)->nullable();
            $table->enum('check_in_method', ['manual', 'geofence', 'qr_code', 'admin'])->default('manual');
            $table->text('notes')->nullable();
            $table->boolean('is_volunteer')->default(false);
            $table->string('department')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index('tenant_id');
            $table->index(['user_id', 'service_date']);
            $table->index(['service_id', 'service_date']);
            $table->index(['service_date', 'status']);
            $table->index('status');
            $table->index('is_volunteer');

            // Unique constraint to prevent duplicate attendance records
            $table->unique(['user_id', 'service_id', 'service_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_records');
    }
};

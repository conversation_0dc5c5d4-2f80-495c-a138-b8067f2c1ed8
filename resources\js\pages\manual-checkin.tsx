import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { useState, useMemo } from 'react';
import { 
    Users, Search, Clock, CheckCircle, XCircle, 
    UserCheck, UserX, Filter, Download, RefreshCw
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Manual Check-in',
        href: '/manual-checkin',
    },
];

// Mock data for church members
interface Member {
    id: number;
    name: string;
    avatar: string;
    department: string;
    phone: string;
    email: string;
    isVolunteer: boolean;
    status: 'present' | 'absent';
    checkInTime?: string;
}

const allMembers: Member[] = [
    { id: 1, name: '<PERSON>', avatar: 'J<PERSON>', department: 'Worship', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 2, name: '<PERSON>', avatar: 'SJ', department: 'Youth', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 3, name: 'Michael Brown', avatar: 'MB', department: 'Admin', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
    { id: 4, name: 'Emily Davis', avatar: 'ED', department: 'Children', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 5, name: 'David Wilson', avatar: 'DW', department: 'Outreach', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
    { id: 6, name: 'Lisa Anderson', avatar: 'LA', department: 'Worship', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 7, name: 'Robert Taylor', avatar: 'RT', department: 'Finance', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
    { id: 8, name: 'Jennifer Martinez', avatar: 'JM', department: 'Youth', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 9, name: 'Christopher Lee', avatar: 'CL', department: 'Music', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 10, name: 'Amanda White', avatar: 'AW', department: 'Children', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 11, name: 'James Garcia', avatar: 'JG', department: 'Security', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
    { id: 12, name: 'Michelle Thompson', avatar: 'MT', department: 'Hospitality', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 13, name: 'Daniel Rodriguez', avatar: 'DR', department: 'Tech', phone: '(*************', email: '<EMAIL>', isVolunteer: true, status: 'absent' },
    { id: 14, name: 'Jessica Moore', avatar: 'JM2', department: 'Prayer', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
    { id: 15, name: 'Kevin Clark', avatar: 'KC', department: 'Maintenance', phone: '(*************', email: '<EMAIL>', isVolunteer: false, status: 'absent' },
];

export default function ManualCheckin() {
    const [members, setMembers] = useState<Member[]>(allMembers);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
    const [selectedMember, setSelectedMember] = useState<string>('');

    // Filter members based on search and department
    const filteredMembers = useMemo(() => {
        return members.filter(member => {
            const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                member.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                member.email.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesDepartment = selectedDepartment === 'all' || member.department === selectedDepartment;
            return matchesSearch && matchesDepartment;
        });
    }, [members, searchTerm, selectedDepartment]);

    // Get unique departments for filter
    const departments = useMemo(() => {
        const depts = [...new Set(members.map(member => member.department))];
        return depts.sort();
    }, [members]);

    // Check in a member
    const checkInMember = (memberId: number) => {
        setMembers(prevMembers =>
            prevMembers.map(member =>
                member.id === memberId
                    ? { ...member, status: 'present', checkInTime: new Date().toLocaleTimeString() }
                    : member
            )
        );
    };

    // Check out a member
    const checkOutMember = (memberId: number) => {
        setMembers(prevMembers =>
            prevMembers.map(member =>
                member.id === memberId
                    ? { ...member, status: 'absent', checkInTime: undefined }
                    : member
            )
        );
    };

    // Quick check-in from dropdown
    const handleQuickCheckin = () => {
        if (selectedMember) {
            const memberId = parseInt(selectedMember);
            checkInMember(memberId);
            setSelectedMember('');
        }
    };

    // Reset all members to absent
    const resetAllMembers = () => {
        setMembers(prevMembers =>
            prevMembers.map(member => ({ ...member, status: 'absent', checkInTime: undefined }))
        );
    };

    const totalPresent = members.filter(m => m.status === 'present').length;
    const totalAbsent = members.filter(m => m.status === 'absent').length;
    const presentMembers = filteredMembers.filter(m => m.status === 'present');
    const absentMembers = filteredMembers.filter(m => m.status === 'absent');

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Manual Check-in" />

            <div className="flex-1 space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Manual Check-in</h1>
                        <p className="text-muted-foreground">
                            Check in members without smartphones • {new Date().toLocaleTimeString()}
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={resetAllMembers}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Reset All
                        </Button>
                        <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {/* Summary Stats */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Present</p>
                                    <p className="text-2xl font-bold text-success">{totalPresent}</p>
                                    <p className="text-sm text-muted-foreground">Checked in today</p>
                                </div>
                                <div className="p-3 rounded-lg bg-success text-white">
                                    <UserCheck className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Absent</p>
                                    <p className="text-2xl font-bold text-muted-foreground">{totalAbsent}</p>
                                    <p className="text-sm text-muted-foreground">Not checked in</p>
                                </div>
                                <div className="p-3 rounded-lg bg-muted text-muted-foreground">
                                    <UserX className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>

                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                    <p className="text-sm font-medium text-muted-foreground">Attendance Rate</p>
                                    <p className="text-2xl font-bold">{Math.round((totalPresent / members.length) * 100)}%</p>
                                    <p className="text-sm text-muted-foreground">{totalPresent} of {members.length} members</p>
                                </div>
                                <div className="p-3 rounded-lg bg-cyan text-white">
                                    <Users className="h-6 w-6" />
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Quick Check-in */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="mb-4">
                            <h3 className="text-lg font-semibold">Quick Check-in</h3>
                            <p className="text-sm text-muted-foreground">Select a member from the dropdown to quickly check them in</p>
                        </div>
                        <div className="flex items-center gap-3">
                            <select
                                value={selectedMember}
                                onChange={(e) => setSelectedMember(e.target.value)}
                                className="flex-1 px-3 py-2 border border-input rounded-md bg-background text-sm"
                            >
                                <option value="">Select a member...</option>
                                {absentMembers.map((member) => (
                                    <option key={member.id} value={member.id.toString()}>
                                        {member.name} - {member.department}
                                    </option>
                                ))}
                            </select>
                            <Button 
                                onClick={handleQuickCheckin} 
                                disabled={!selectedMember}
                                className="bg-cyan hover:bg-cyan/90"
                            >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Check In
                            </Button>
                        </div>
                    </div>
                </Card>

                {/* Search and Filter */}
                <Card className="shadow-sm">
                    <div className="p-6">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <input
                                    type="text"
                                    placeholder="Search members by name, department, or email..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm"
                                />
                            </div>
                            <select
                                value={selectedDepartment}
                                onChange={(e) => setSelectedDepartment(e.target.value)}
                                className="px-3 py-2 border border-input rounded-md bg-background text-sm"
                            >
                                <option value="all">All Departments</option>
                                {departments.map((dept) => (
                                    <option key={dept} value={dept}>{dept}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                </Card>

                {/* Members List */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Present Members */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-4 flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-semibold text-success">Present Members</h3>
                                    <p className="text-sm text-muted-foreground">{presentMembers.length} members checked in</p>
                                </div>
                                <Badge className="bg-success text-white">
                                    {presentMembers.length}
                                </Badge>
                            </div>
                            <div className="space-y-3 max-h-96 overflow-y-auto">
                                {presentMembers.length === 0 ? (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <UserCheck className="h-12 w-12 mx-auto mb-2 opacity-50" />
                                        <p>No members checked in yet</p>
                                    </div>
                                ) : (
                                    presentMembers.map((member) => (
                                        <div key={member.id} className="flex items-center justify-between p-3 border border-border rounded-lg bg-success/5">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 rounded-full bg-success text-white flex items-center justify-center text-sm font-medium">
                                                    {member.avatar}
                                                </div>
                                                <div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-medium">{member.name}</span>
                                                        {member.isVolunteer && (
                                                            <Badge variant="outline" className="text-xs">Volunteer</Badge>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                                        <span>{member.department}</span>
                                                        {member.checkInTime && (
                                                            <>
                                                                <span>•</span>
                                                                <div className="flex items-center gap-1">
                                                                    <Clock className="h-3 w-3" />
                                                                    <span>{member.checkInTime}</span>
                                                                </div>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => checkOutMember(member.id)}
                                                className="text-destructive border-destructive hover:bg-destructive hover:text-white"
                                            >
                                                <XCircle className="h-4 w-4 mr-1" />
                                                Check Out
                                            </Button>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    </Card>

                    {/* Absent Members */}
                    <Card className="shadow-sm">
                        <div className="p-6">
                            <div className="mb-4 flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-semibold text-muted-foreground">Absent Members</h3>
                                    <p className="text-sm text-muted-foreground">{absentMembers.length} members not checked in</p>
                                </div>
                                <Badge variant="outline">
                                    {absentMembers.length}
                                </Badge>
                            </div>
                            <div className="space-y-3 max-h-96 overflow-y-auto">
                                {absentMembers.length === 0 ? (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <CheckCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
                                        <p>All members are checked in!</p>
                                    </div>
                                ) : (
                                    absentMembers.map((member) => (
                                        <div key={member.id} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-sm font-medium">
                                                    {member.avatar}
                                                </div>
                                                <div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-medium">{member.name}</span>
                                                        {member.isVolunteer && (
                                                            <Badge variant="outline" className="text-xs">Volunteer</Badge>
                                                        )}
                                                    </div>
                                                    <p className="text-sm text-muted-foreground">{member.department}</p>
                                                </div>
                                            </div>
                                            <Button
                                                onClick={() => checkInMember(member.id)}
                                                size="sm"
                                                className="bg-cyan hover:bg-cyan/90"
                                            >
                                                <CheckCircle className="h-4 w-4 mr-1" />
                                                Check In
                                            </Button>
                                        </div>
                                    ))
                                )}
                            </div>
                        </div>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}

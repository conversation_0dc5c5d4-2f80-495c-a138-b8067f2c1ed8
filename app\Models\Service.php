<?php

namespace App\Models;

use App\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends TenantModel
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'name',
        'description',
        'service_type',
        'day_of_week',
        'start_time',
        'end_time',
        'is_active',
        'target_attendance',
        'location',
        'recurring',
        'frequency',
    ];

    protected function casts(): array
    {
        return [
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'is_active' => 'boolean',
            'recurring' => 'boolean',
            'target_attendance' => 'integer',
        ];
    }

    /**
     * Get all attendance records for this service.
     */
    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceRecord::class);
    }

    /**
     * Get active services only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get services by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('service_type', $type);
    }

    /**
     * Get services by day of week.
     */
    public function scopeByDay($query, int $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }
}

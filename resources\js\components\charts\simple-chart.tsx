import { Card } from '@/components/ui/card';

interface SimpleChartProps {
    title: string;
    data: Array<{ name: string; value: number; color?: string }>;
    type?: 'bar' | 'line';
}

export function SimpleChart({ title, data, type = 'bar' }: SimpleChartProps) {
    const maxValue = Math.max(...data.map((d) => d.value));

    return (
        <Card className="shadow-sm transition-all duration-200 hover:shadow-md">
            <div className="p-5">
                <h3 className="mb-5 text-lg font-semibold text-foreground">{title}</h3>

                <div className="space-y-3">
                    {data.map((item, index) => {
                        const percentage = (item.value / maxValue) * 100;
                        const color = item.color || 'bg-primary';

                        return (
                            <div key={index} className="space-y-2">
                                <div className="flex justify-between text-sm">
                                    <span className="truncate font-medium text-foreground">{item.name}</span>
                                    <span className="ml-2 flex-shrink-0 text-muted-foreground">{item.value.toLocaleString()}</span>
                                </div>
                                <div className="h-2 w-full rounded-full bg-muted/50">
                                    <div className={`h-2 rounded-full transition-all duration-500 ${color}`} style={{ width: `${percentage}%` }} />
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </Card>
    );
}

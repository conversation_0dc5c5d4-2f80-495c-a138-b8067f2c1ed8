import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface AttendanceBarChartProps {
    title: string;
    description?: string;
    data: Array<{
        name: string;
        attendance: number;
        target?: number;
        [key: string]: any;
    }>;
    height?: number;
    showTarget?: boolean;
    loading?: boolean;
    emptyMessage?: string;
}

export function AttendanceBarChart({
    title,
    description,
    data,
    height = 320,
    showTarget = false,
    loading = false,
    emptyMessage = 'No data available',
}: AttendanceBarChartProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
            <CardContent>
                {data.length > 0 ? (
                    <div style={{ height }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={data}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                <XAxis 
                                    dataKey="name" 
                                    stroke="#6b7280"
                                    tick={{ fontSize: 12 }}
                                />
                                <YAxis stroke="#6b7280" />
                                <Tooltip 
                                    contentStyle={{ 
                                        backgroundColor: 'white', 
                                        border: '1px solid #e5e7eb',
                                        borderRadius: '8px'
                                    }} 
                                />
                                <Bar 
                                    dataKey="attendance" 
                                    fill="#3b82f6" 
                                    name="Attendance"
                                    radius={[4, 4, 0, 0]}
                                />
                                {showTarget && (
                                    <Bar 
                                        dataKey="target" 
                                        fill="#e5e7eb" 
                                        name="Target"
                                        radius={[4, 4, 0, 0]}
                                    />
                                )}
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                ) : (
                    <div className="flex items-center justify-center text-muted-foreground" style={{ height }}>
                        {loading ? 'Loading chart data...' : emptyMessage}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

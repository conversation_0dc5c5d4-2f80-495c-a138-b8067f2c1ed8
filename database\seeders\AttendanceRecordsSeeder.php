<?php

namespace Database\Seeders;

use App\Models\AttendanceRecord;
use App\Models\Service;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AttendanceRecordsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $services = Service::where('is_active', true)->get();

        if ($users->isEmpty() || $services->isEmpty()) {
            $this->command->warn('No users or services found. Please run UserSeeder and ServicesSeeder first.');
            return;
        }

        // Generate attendance records for the past 12 months
        $startDate = Carbon::now()->subMonths(12)->startOfMonth();
        $endDate = Carbon::now();

        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            foreach ($services as $service) {
                // Skip if this service doesn't occur on this day
                if ($service->day_of_week !== $current->dayOfWeek) {
                    $current->addDay();
                    continue;
                }

                // Skip monthly services if not the right week
                if ($service->frequency === 'monthly' && $current->weekOfMonth !== 1) {
                    $current->addDay();
                    continue;
                }

                // Generate attendance for this service
                $this->generateAttendanceForService($service, $current->copy(), $users);
            }
            $current->addDay();
        }
    }

    /**
     * Generate attendance records for a specific service and date.
     */
    private function generateAttendanceForService(Service $service, Carbon $date, $users): void
    {
        // Calculate attendance based on service type and seasonal variations
        $baseAttendance = $this->getBaseAttendance($service->service_type);
        $seasonalMultiplier = $this->getSeasonalMultiplier($date);
        $expectedAttendance = (int) ($baseAttendance * $seasonalMultiplier);

        // Add some randomness (±20%)
        $variance = rand(-20, 20) / 100;
        $actualAttendance = max(1, (int) ($expectedAttendance * (1 + $variance)));

        // Randomly select users to attend
        $attendees = $users->random(min($actualAttendance, $users->count()));

        foreach ($attendees as $user) {
            // Skip if user already has attendance record for this service and date
            $existingRecord = AttendanceRecord::where('user_id', $user->id)
                ->where('service_id', $service->id)
                ->where('service_date', $date->toDateString())
                ->first();

            if ($existingRecord) {
                continue;
            }

            // Determine check-in time (some arrive early, some late)
            $serviceStart = Carbon::parse($service->start_time);
            $checkInVariance = rand(-15, 30); // -15 to +30 minutes
            $checkInTime = $date->copy()
                ->setTime($serviceStart->hour, $serviceStart->minute)
                ->addMinutes($checkInVariance);

            // Determine status based on check-in time
            $status = 'present';
            if ($checkInVariance > 15) {
                $status = 'late';
            }

            // Some people might check out early
            $serviceEnd = Carbon::parse($service->end_time);
            $checkOutTime = null;
            if (rand(1, 10) <= 2) { // 20% chance of early checkout
                $checkOutVariance = rand(-30, 0); // Leave up to 30 minutes early
                $checkOutTime = $date->copy()
                    ->setTime($serviceEnd->hour, $serviceEnd->minute)
                    ->addMinutes($checkOutVariance);
            }

            // Determine if user is a volunteer (20% chance)
            $isVolunteer = rand(1, 100) <= 20;
            $department = $isVolunteer ? $this->getRandomDepartment() : null;

            AttendanceRecord::create([
                'user_id' => $user->id,
                'service_id' => $service->id,
                'service_date' => $date->toDateString(),
                'check_in_time' => $checkInTime,
                'check_out_time' => $checkOutTime,
                'status' => $status,
                'location_lat' => 40.7128 + (rand(-100, 100) / 10000), // Near church location
                'location_lng' => -74.0060 + (rand(-100, 100) / 10000),
                'check_in_method' => $this->getRandomCheckInMethod(),
                'notes' => rand(1, 100) <= 5 ? 'Special note for this attendance' : null,
                'is_volunteer' => $isVolunteer,
                'department' => $department,
            ]);
        }
    }

    /**
     * Get base attendance for different service types.
     */
    private function getBaseAttendance(string $serviceType): int
    {
        return match ($serviceType) {
            'sunday_morning' => 245,
            'sunday_evening' => 95,
            'wednesday_evening' => 85,
            'youth' => 45,
            'children' => 65,
            'fellowship' => 32,
            'bible_study' => 28,
            'special' => 280,
            default => 50,
        };
    }

    /**
     * Get seasonal multiplier for attendance.
     */
    private function getSeasonalMultiplier(Carbon $date): float
    {
        $month = $date->month;

        return match ($month) {
            12, 1 => 1.2, // Christmas/New Year boost
            3, 4 => 1.15, // Easter season
            6, 7, 8 => 0.85, // Summer vacation dip
            9 => 1.1, // Back to school boost
            default => 1.0,
        };
    }

    /**
     * Get random check-in method.
     */
    private function getRandomCheckInMethod(): string
    {
        $methods = ['manual', 'geofence', 'qr_code', 'admin'];
        return $methods[array_rand($methods)];
    }

    /**
     * Get random department for volunteers.
     */
    private function getRandomDepartment(): string
    {
        $departments = ['Worship', 'Youth', 'Children', 'Admin', 'Hospitality', 'Media', 'Security'];
        return $departments[array_rand($departments)];
    }
}

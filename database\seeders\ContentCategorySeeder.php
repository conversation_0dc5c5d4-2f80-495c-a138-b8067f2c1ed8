<?php

namespace Database\Seeders;

use App\Models\ContentCategory;
use Illuminate\Database\Seeder;

class ContentCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Video Categories
            [
                'name' => 'Sunday Sermons',
                'slug' => 'sunday-sermons',
                'description' => 'Weekly Sunday service sermons and messages',
                'type' => 'video',
                'color' => '#4489e4',
                'icon' => 'Church',
                'sort_order' => 1,
            ],
            [
                'name' => 'Bible Study',
                'slug' => 'bible-study',
                'description' => 'Midweek Bible study sessions and teachings',
                'type' => 'video',
                'color' => '#22c55e',
                'icon' => 'BookOpen',
                'sort_order' => 2,
            ],
            [
                'name' => 'Testimonies',
                'slug' => 'testimonies',
                'description' => 'Personal testimonies and faith stories',
                'type' => 'video',
                'color' => '#f9c851',
                'icon' => 'Heart',
                'sort_order' => 3,
            ],
            [
                'name' => 'Special Events',
                'slug' => 'special-events',
                'description' => 'Church events, conferences, and special services',
                'type' => 'video',
                'color' => '#f24f7c',
                'icon' => 'Calendar',
                'sort_order' => 4,
            ],

            // Hymn Categories
            [
                'name' => 'Traditional Hymns',
                'slug' => 'traditional-hymns',
                'description' => 'Classic hymns and traditional worship songs',
                'type' => 'hymn',
                'color' => '#716cb0',
                'icon' => 'Music',
                'sort_order' => 5,
            ],
            [
                'name' => 'Contemporary Worship',
                'slug' => 'contemporary-worship',
                'description' => 'Modern worship songs and contemporary music',
                'type' => 'hymn',
                'color' => '#33b0e0',
                'icon' => 'Mic',
                'sort_order' => 6,
            ],
            [
                'name' => 'Seasonal Songs',
                'slug' => 'seasonal-songs',
                'description' => 'Christmas, Easter, and other seasonal hymns',
                'type' => 'hymn',
                'color' => '#f7931e',
                'icon' => 'Snowflake',
                'sort_order' => 7,
            ],

            // Devotional Categories
            [
                'name' => 'Daily Devotions',
                'slug' => 'daily-devotions',
                'description' => 'Daily devotional readings and reflections',
                'type' => 'devotional',
                'color' => '#3cc0c3',
                'icon' => 'Sun',
                'sort_order' => 8,
            ],
            [
                'name' => 'Prayer & Meditation',
                'slug' => 'prayer-meditation',
                'description' => 'Prayer guides and meditation resources',
                'type' => 'devotional',
                'color' => '#d03f3f',
                'icon' => 'Hands',
                'sort_order' => 9,
            ],
            [
                'name' => 'Inspirational',
                'slug' => 'inspirational',
                'description' => 'Inspirational quotes and encouraging messages',
                'type' => 'devotional',
                'color' => '#34b0e0',
                'icon' => 'Sparkles',
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $category) {
            ContentCategory::create($category);
        }
    }
}

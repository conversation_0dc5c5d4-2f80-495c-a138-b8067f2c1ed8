<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints for all tenant_id columns
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('church_settings', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('content_categories', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('videos', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('hymns', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('devotionals', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });

        Schema::table('attendance_records', function (Blueprint $table) {
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('church_settings', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('content_categories', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('videos', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('hymns', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('devotionals', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('services', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });

        Schema::table('attendance_records', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
        });
    }
};

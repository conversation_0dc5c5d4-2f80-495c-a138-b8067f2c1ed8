import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

interface AttendancePieChartProps {
    title: string;
    description?: string;
    data: Array<{
        name: string;
        value: number;
        color?: string;
    }>;
    height?: number;
    loading?: boolean;
    emptyMessage?: string;
    showLabels?: boolean;
    showLegend?: boolean;
}

const DEFAULT_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16'];

export function AttendancePieChart({
    title,
    description,
    data,
    height = 320,
    loading = false,
    emptyMessage = 'No data available',
    showLabels = true,
    showLegend = true,
}: AttendancePieChartProps) {
    // Assign colors to data items if not provided
    const dataWithColors = data.map((item, index) => ({
        ...item,
        color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length],
    }));

    const renderCustomLabel = ({ name, percent }: any) => {
        return showLabels ? `${name} ${(percent * 100).toFixed(0)}%` : '';
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
            <CardContent>
                {data.length > 0 ? (
                    <div className="space-y-4">
                        <div style={{ height }}>
                            <ResponsiveContainer width="100%" height="100%">
                                <PieChart>
                                    <Pie
                                        data={dataWithColors}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={renderCustomLabel}
                                        outerRadius={Math.min(height * 0.3, 100)}
                                        fill="#8884d8"
                                        dataKey="value"
                                    >
                                        {dataWithColors.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Pie>
                                    <Tooltip 
                                        formatter={(value: number) => [value.toLocaleString(), 'Value']}
                                        contentStyle={{ 
                                            backgroundColor: 'white', 
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '8px'
                                        }}
                                    />
                                </PieChart>
                            </ResponsiveContainer>
                        </div>
                        
                        {showLegend && (
                            <div className="flex flex-wrap gap-4 justify-center">
                                {dataWithColors.map((item, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <div 
                                            className="w-3 h-3 rounded-full" 
                                            style={{ backgroundColor: item.color }}
                                        />
                                        <span className="text-sm text-muted-foreground">
                                            {item.name}: {item.value.toLocaleString()}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="flex items-center justify-center text-muted-foreground" style={{ height }}>
                        {loading ? 'Loading chart data...' : emptyMessage}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { 
    Building2, 
    Users, 
    Calendar,
    BarChart3,
    Settings,
    ArrowLeft,
    ExternalLink,
    Shield,
    Database,
    Clock
} from 'lucide-react';

interface Church {
    id: number;
    name: string;
    slug: string;
    domain: string;
    status: 'active' | 'inactive' | 'suspended';
    plan_type: string;
    created_at: string;
    trial_ends_at?: string;
    settings: any;
    users: Array<{
        id: number;
        name: string;
        email: string;
        created_at: string;
    }>;
}

interface ChurchStats {
    total_users: number;
    total_services: number;
    total_attendance: number;
}

interface ChurchShowProps {
    church: Church;
    stats: ChurchStats;
}

export default function ChurchShow({ church, stats }: ChurchShowProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Church Management',
            href: '/super-admin/churches',
        },
        {
            title: church.name,
            href: `/super-admin/churches/${church.id}`,
        },
    ];

    const handleStatusChange = (newStatus: string) => {
        router.patch(`/super-admin/churches/${church.id}/status`, {
            status: newStatus,
        });
    };

    const handleSwitchToChurch = () => {
        router.post(`/super-admin/churches/${church.id}/switch`);
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">Active</Badge>;
            case 'inactive':
                return <Badge variant="secondary">Inactive</Badge>;
            case 'suspended':
                return <Badge variant="destructive">Suspended</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${church.name} - Church Details`} />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href="/super-admin/churches">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Churches
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                                <Building2 className="h-8 w-8" />
                                {church.name}
                            </h1>
                            <p className="text-muted-foreground">
                                Detailed information and management for this church
                            </p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={handleSwitchToChurch}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Switch to Church
                        </Button>
                        <Button>
                            <Settings className="mr-2 h-4 w-4" />
                            Manage Settings
                        </Button>
                    </div>
                </div>

                {/* Church Overview */}
                <div className="grid gap-6 lg:grid-cols-3">
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle>Church Information</CardTitle>
                            <CardDescription>
                                Basic details and configuration
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Church Name</label>
                                    <p className="text-lg font-semibold">{church.name}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                                    <div className="mt-1">
                                        {getStatusBadge(church.status)}
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Domain</label>
                                    <p className="font-mono text-sm bg-muted px-2 py-1 rounded">{church.domain}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Slug</label>
                                    <p className="font-mono text-sm">{church.slug}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Plan Type</label>
                                    <Badge variant="outline" className="mt-1">{church.plan_type}</Badge>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                                    <p className="text-sm">{new Date(church.created_at).toLocaleDateString()}</p>
                                </div>
                            </div>
                            
                            {church.trial_ends_at && (
                                <>
                                    <Separator />
                                    <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <Clock className="h-4 w-4 text-yellow-600" />
                                        <div>
                                            <p className="text-sm font-medium text-yellow-800">Trial Period</p>
                                            <p className="text-xs text-yellow-600">
                                                Ends on {new Date(church.trial_ends_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </>
                            )}
                        </CardContent>
                    </Card>

                    {/* Stats Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Statistics</CardTitle>
                            <CardDescription>
                                Usage metrics for this church
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">Total Users</span>
                                </div>
                                <span className="font-semibold">{stats.total_users}</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">Services</span>
                                </div>
                                <span className="font-semibold">{stats.total_services}</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">Attendance Records</span>
                                </div>
                                <span className="font-semibold">{stats.total_attendance}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Users ({church.users.length})</CardTitle>
                        <CardDescription>
                            All users registered under this church
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Email</TableHead>
                                    <TableHead>Joined</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {church.users.map((user) => (
                                    <TableRow key={user.id}>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                                    {user.name.charAt(0).toUpperCase()}
                                                </div>
                                                <span className="font-medium">{user.name}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>{user.email}</TableCell>
                                        <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                                        <TableCell>
                                            <Button variant="ghost" size="sm">
                                                <Shield className="h-4 w-4" />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>

                {/* Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-red-600">Danger Zone</CardTitle>
                        <CardDescription>
                            Irreversible actions for this church
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
                            <div>
                                <h4 className="font-medium">Suspend Church</h4>
                                <p className="text-sm text-muted-foreground">
                                    Temporarily disable access to this church
                                </p>
                            </div>
                            <Button 
                                variant="destructive" 
                                onClick={() => handleStatusChange('suspended')}
                                disabled={church.status === 'suspended'}
                            >
                                Suspend
                            </Button>
                        </div>
                        <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
                            <div>
                                <h4 className="font-medium">Delete Church</h4>
                                <p className="text-sm text-muted-foreground">
                                    Permanently delete this church and all associated data
                                </p>
                            </div>
                            <Button variant="destructive">
                                <Database className="mr-2 h-4 w-4" />
                                Delete
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}

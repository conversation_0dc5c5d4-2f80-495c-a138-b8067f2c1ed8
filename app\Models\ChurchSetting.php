<?php

namespace App\Models;

use App\TenantModel;

class ChurchSetting extends TenantModel
{
    protected $fillable = [
        'tenant_id',
        'church_name',
        'church_address',
        'church_phone',
        'church_email',
        'church_website',
        'social_media_links',
        'church_description',
        'mission_statement',
        'service_times',
        'leadership_info',
        'latitude',
        'longitude',
        'geofence_radius',
        'attendance_preferences',
        'manual_checkin_settings',
        'notification_settings',
        'attendance_report_configs',
        'user_roles_permissions',
        'notification_preferences',
        'backup_settings',
        'privacy_security_configs',
        'mobile_app_config',
        'third_party_integrations',
        'api_keys',
    ];

    protected function casts(): array
    {
        return [
            'social_media_links' => 'array',
            'service_times' => 'array',
            'leadership_info' => 'array',
            'attendance_preferences' => 'array',
            'manual_checkin_settings' => 'array',
            'notification_settings' => 'array',
            'attendance_report_configs' => 'array',
            'user_roles_permissions' => 'array',
            'notification_preferences' => 'array',
            'backup_settings' => 'array',
            'privacy_security_configs' => 'array',
            'mobile_app_config' => 'array',
            'third_party_integrations' => 'array',
            'api_keys' => 'array',
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
        ];
    }

    public static function current(): self
    {
        // Get current tenant's church settings
        if (app()->bound('tenant')) {
            $tenant = app('tenant');
            return static::where('tenant_id', $tenant->id)->first()
                ?? static::create(['tenant_id' => $tenant->id]);
        }

        // For super admin or no tenant context, return first available settings
        return static::whereNull('tenant_id')->first()
            ?? static::create(['tenant_id' => null]);
    }
}

import { NavMain } from '@/components/layout/nav-main';
import { NavUser } from '@/components/layout/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BarChart3, Bell, Building2, CheckSquare, FileImage, FileText, LayoutGrid, Settings, Shield, Users, Users2 } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Admin Analytics',
        href: '/admin/analytics',
        icon: BarChart3,
    },
    {
        title: 'Live Attendance',
        href: '/live-attendance',
        icon: Users,
    },
    {
        title: 'Manual Checkin',
        href: '/manual-checkin',
        icon: CheckSquare,
    },
    {
        title: 'Reports',
        href: '/reports',
        icon: FileText,
    },
    {
        title: 'Members',
        href: '/members',
        icon: Users2,
    },
    {
        title: 'Content Management',
        href: '/content-management',
        icon: FileImage,
    },
    {
        title: 'Notification',
        href: '/notifications',
        icon: Bell,
    },
    {
        title: 'Church Settings',
        href: '/church-settings',
        icon: Settings,
    },
];

export function AppSidebar() {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    const isSuperAdmin = user?.is_super_admin;

    // Super admin navigation items (flat structure, no dropdowns)
    const superAdminNavItems: NavItem[] = [
        {
            title: 'Church Management',
            href: '/super-admin/churches',
            icon: Building2,
        },
        {
            title: 'Church Administration',
            href: '/super-admin/administration',
            icon: Shield,
        },
    ];

    // Combine regular nav items with super admin items if user is super admin
    const allNavItems = isSuperAdmin ? [...mainNavItems, ...superAdminNavItems] : mainNavItems;

    return (
        <Sidebar collapsible="icon" variant="inset" className="border-r-0">
            <SidebarHeader className="border-b border-sidebar-border/50 bg-sidebar">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild className="hover:bg-sidebar-accent/50">
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="bg-sidebar">
                <NavMain items={allNavItems} />
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border/50 bg-sidebar">
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}

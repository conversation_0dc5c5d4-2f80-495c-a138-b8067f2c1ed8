import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type ServiceAttendanceData } from '@/types/reports';
import { Calendar, TrendingDown, TrendingUp, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, Cell, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface ServiceAttendanceReportProps {
    initialData?: ServiceAttendanceData[];
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

export function ServiceAttendanceReport({ initialData = [] }: ServiceAttendanceReportProps) {
    const [data, setData] = useState<ServiceAttendanceData[]>(initialData);
    const [loading, setLoading] = useState(false);
    const [filters, setFilters] = useState({
        service_type: 'all',
        period: 'month',
    });

    const fetchServiceData = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (filters.service_type !== 'all') params.append('service_type', filters.service_type);
            params.append('period', filters.period);

            const response = await fetch(`/reports/service-attendance?${params.toString()}`);
            const result = await response.json();
            setData(result.data || []);
        } catch (error) {
            console.error('Error fetching service attendance data:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchServiceData();
    }, []);

    const handleFilterChange = (key: string, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const totalServices = data.reduce((sum, item) => sum + item.total_services, 0);
    const totalAttendance = data.reduce((sum, item) => sum + (item.average_attendance * item.total_services), 0);
    const overallAverage = totalServices > 0 ? Math.round(totalAttendance / totalServices) : 0;

    // Prepare data for pie chart
    const pieData = data.map((item, index) => ({
        name: item.service_type,
        value: item.average_attendance * item.total_services,
        color: COLORS[index % COLORS.length],
    }));

    const getTrendIcon = (trend: string) => {
        switch (trend) {
            case 'up':
                return <TrendingUp className="h-4 w-4 text-green-600" />;
            case 'down':
                return <TrendingDown className="h-4 w-4 text-red-600" />;
            default:
                return <Users className="h-4 w-4 text-gray-600" />;
        }
    };

    const getTrendColor = (trend: string) => {
        switch (trend) {
            case 'up':
                return 'text-green-600';
            case 'down':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Service Attendance Analysis
                    </CardTitle>
                    <CardDescription>Compare attendance across different service types</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center gap-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Service Type</label>
                            <Select value={filters.service_type} onValueChange={(value) => handleFilterChange('service_type', value)}>
                                <SelectTrigger className="w-48">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Services</SelectItem>
                                    <SelectItem value="sunday_morning">Sunday Morning</SelectItem>
                                    <SelectItem value="wednesday_evening">Wednesday Evening</SelectItem>
                                    <SelectItem value="youth">Youth Service</SelectItem>
                                    <SelectItem value="special">Special Events</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Period</label>
                            <Select value={filters.period} onValueChange={(value) => handleFilterChange('period', value)}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="week">Week</SelectItem>
                                    <SelectItem value="month">Month</SelectItem>
                                    <SelectItem value="quarter">Quarter</SelectItem>
                                    <SelectItem value="year">Year</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <Button onClick={fetchServiceData} disabled={loading} variant="outline">
                            {loading ? 'Loading...' : 'Apply Filters'}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Services</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalServices}</div>
                        <p className="text-xs text-muted-foreground">Across all types</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Overall Average</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{overallAverage}</div>
                        <p className="text-xs text-muted-foreground">Per service</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Service Types</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{data.length}</div>
                        <p className="text-xs text-muted-foreground">Active types</p>
                    </CardContent>
                </Card>
            </div>

            {/* Charts */}
            <div className="grid gap-6 lg:grid-cols-2">
                {/* Bar Chart */}
                <Card>
                    <CardHeader>
                        <CardTitle>Average Attendance by Service</CardTitle>
                        <CardDescription>Compare average attendance across service types</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {data.length > 0 ? (
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart data={data} layout="horizontal">
                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                        <XAxis type="number" stroke="#6b7280" />
                                        <YAxis 
                                            type="category" 
                                            dataKey="service_type" 
                                            stroke="#6b7280"
                                            tick={{ fontSize: 12 }}
                                            width={120}
                                        />
                                        <Tooltip 
                                            contentStyle={{ 
                                                backgroundColor: 'white', 
                                                border: '1px solid #e5e7eb',
                                                borderRadius: '8px'
                                            }} 
                                        />
                                        <Bar dataKey="average_attendance" fill="#3b82f6" />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        ) : (
                            <div className="flex h-80 items-center justify-center text-muted-foreground">
                                {loading ? 'Loading service data...' : 'No service data available.'}
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pie Chart */}
                <Card>
                    <CardHeader>
                        <CardTitle>Attendance Distribution</CardTitle>
                        <CardDescription>Total attendance share by service type</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {pieData.length > 0 ? (
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={pieData}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={false}
                                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                            outerRadius={80}
                                            fill="#8884d8"
                                            dataKey="value"
                                        >
                                            {pieData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip 
                                            formatter={(value: number) => [value.toLocaleString(), 'Total Attendance']}
                                        />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>
                        ) : (
                            <div className="flex h-80 items-center justify-center text-muted-foreground">
                                No distribution data available.
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Service Details Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Service Performance Details</CardTitle>
                    <CardDescription>Detailed breakdown of each service type</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Service Type</th>
                                        <th className="text-right p-2">Total Services</th>
                                        <th className="text-right p-2">Avg Attendance</th>
                                        <th className="text-right p-2">Highest</th>
                                        <th className="text-right p-2">Lowest</th>
                                        <th className="text-center p-2">Trend</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {data.map((item, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="p-2 font-medium">{item.service_type}</td>
                                            <td className="text-right p-2">{item.total_services}</td>
                                            <td className="text-right p-2">{item.average_attendance}</td>
                                            <td className="text-right p-2 text-green-600">{item.highest}</td>
                                            <td className="text-right p-2 text-red-600">{item.lowest}</td>
                                            <td className="text-center p-2">
                                                <div className="flex items-center justify-center gap-1">
                                                    {getTrendIcon(item.trend)}
                                                    <span className={`text-xs capitalize ${getTrendColor(item.trend)}`}>
                                                        {item.trend}
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground py-8">
                            No service performance data available.
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

import { AppContent } from '@/components/layout/app-content';
import { AppShell } from '@/components/layout/app-shell';
import { SuperAdminSidebar } from '@/components/layout/super-admin-sidebar';
import { AppSidebarHeaderFixed as App<PERSON><PERSON>barHeader } from '@/components/layout/app-sidebar-header-fixed';
import { type BreadcrumbItem } from '@/types';
import { type PropsWithChildren } from 'react';

export default function SuperAdminSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    return (
        <AppShell variant="sidebar">
            <SuperAdminSidebar />
            <AppContent variant="sidebar" className="overflow-x-hidden">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                {children}
            </AppContent>
        </AppShell>
    );
}

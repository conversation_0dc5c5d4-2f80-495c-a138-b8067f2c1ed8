/**
 * Velonic Theme Context & Hook
 * Comprehensive theme management based on Velonic design system
 */

import type { SidebarTheme, ThemeConfig, ThemeContextType, TopbarTheme, VelonicBrandColor } from '@/types/theme';
import { createContext, useContext, useEffect, useState } from 'react';

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Local storage keys
const THEME_STORAGE_KEY = 'velonic-theme-config';

// Default configuration
const defaultConfig: ThemeConfig = {
    mode: 'light',
    sidebar: 'light',
    topbar: 'light',
    primaryColor: 'cyan',
};

interface ThemeProviderProps {
    children: React.ReactNode;
    defaultTheme?: Partial<ThemeConfig>;
    storageKey?: string;
}

export function ThemeProvider({ children, defaultTheme = defaultConfig, storageKey = THEME_STORAGE_KEY }: ThemeProviderProps) {
    const [config, setConfig] = useState<ThemeConfig>(() => {
        // Try to load from localStorage on initial load
        if (typeof window !== 'undefined') {
            try {
                const stored = localStorage.getItem(storageKey);
                if (stored) {
                    const parsed = JSON.parse(stored) as Partial<ThemeConfig>;
                    return { ...defaultConfig, ...defaultTheme, ...parsed };
                }
            } catch (error) {
                console.warn('Failed to parse theme config from localStorage:', error);
            }
        }
        return { ...defaultConfig, ...defaultTheme };
    });

    // Persist to localStorage whenever config changes
    useEffect(() => {
        try {
            localStorage.setItem(storageKey, JSON.stringify(config));
        } catch (error) {
            console.warn('Failed to save theme config to localStorage:', error);
        }
    }, [config, storageKey]);

    // Apply theme classes to document
    useEffect(() => {
        const root = window.document.documentElement;

        // Remove existing theme classes
        root.classList.remove('light', 'dark');
        root.classList.remove('sidebar-light', 'sidebar-dark');
        root.classList.remove('topbar-light', 'topbar-dark');

        // Apply current theme classes
        root.classList.add(config.mode);
        root.classList.add(`sidebar-${config.sidebar}`);
        root.classList.add(`topbar-${config.topbar}`);

        // Update primary color CSS custom property
        root.style.setProperty('--primary-brand-color', getVelonicColorValue(config.primaryColor));

        // Update data attributes for CSS targeting
        root.setAttribute('data-theme-mode', config.mode);
        root.setAttribute('data-sidebar-theme', config.sidebar);
        root.setAttribute('data-topbar-theme', config.topbar);
        root.setAttribute('data-primary-color', config.primaryColor);
    }, [config]);

    // Theme update functions
    const updateTheme = (updates: Partial<ThemeConfig>) => {
        setConfig((prev) => ({ ...prev, ...updates }));
    };

    const toggleTheme = () => {
        setConfig((prev) => ({
            ...prev,
            mode: prev.mode === 'light' ? 'dark' : 'light',
        }));
    };

    const setSidebarTheme = (sidebar: SidebarTheme) => {
        setConfig((prev) => ({ ...prev, sidebar }));
    };

    const setTopbarTheme = (topbar: TopbarTheme) => {
        setConfig((prev) => ({ ...prev, topbar }));
    };

    const setPrimaryColor = (primaryColor: VelonicBrandColor) => {
        setConfig((prev) => ({ ...prev, primaryColor }));
    };

    // Color and dimension getters
    const colors = getThemeColors(config);
    const dimensions = getLayoutDimensions();

    const value: ThemeContextType = {
        config,
        colors,
        dimensions,
        updateTheme,
        toggleTheme,
        setSidebarTheme,
        setTopbarTheme,
        setPrimaryColor,
    };

    return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

// Custom hook to use theme
export function useTheme() {
    const context = useContext(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}

// Utility hook for theme-specific styling
export function useThemeStyles() {
    const { config, colors } = useTheme();

    return {
        // Mode helpers
        isLight: config.mode === 'light',
        isDark: config.mode === 'dark',

        // Sidebar helpers
        isSidebarLight: config.sidebar === 'light',
        isSidebarDark: config.sidebar === 'dark',

        // Topbar helpers
        isTopbarLight: config.topbar === 'light',
        isTopbarDark: config.topbar === 'dark',

        // Color helpers
        primary: colors.primary,
        primaryColor: config.primaryColor,

        // Dynamic classes
        sidebarClass: `sidebar-${config.sidebar}`,
        topbarClass: `topbar-${config.topbar}`,
        modeClass: config.mode,

        // CSS custom properties
        cssVars: {
            '--theme-primary': colors.primary,
            '--theme-mode': config.mode,
            '--sidebar-theme': config.sidebar,
            '--topbar-theme': config.topbar,
        } as React.CSSProperties,
    };
}

// Theme-aware conditional styling hook
export function useThemeConditionalStyles() {
    const { config } = useTheme();

    return {
        // Mode-based styles
        onLight: function <T>(lightStyle: T, darkStyle?: T): T | undefined {
            return config.mode === 'light' ? lightStyle : darkStyle;
        },

        onDark: function <T>(darkStyle: T, lightStyle?: T): T | undefined {
            return config.mode === 'dark' ? darkStyle : lightStyle;
        },

        // Sidebar-based styles
        onSidebarLight: function <T>(lightStyle: T, darkStyle?: T): T | undefined {
            return config.sidebar === 'light' ? lightStyle : darkStyle;
        },

        onSidebarDark: function <T>(darkStyle: T, lightStyle?: T): T | undefined {
            return config.sidebar === 'dark' ? darkStyle : lightStyle;
        },

        // Topbar-based styles
        onTopbarLight: function <T>(lightStyle: T, darkStyle?: T): T | undefined {
            return config.topbar === 'light' ? lightStyle : darkStyle;
        },

        onTopbarDark: function <T>(darkStyle: T, lightStyle?: T): T | undefined {
            return config.topbar === 'dark' ? darkStyle : lightStyle;
        },
    };
}

// Helper functions
function getVelonicColorValue(color: VelonicBrandColor): string {
    const colorMap: Record<VelonicBrandColor, string> = {
        blue: '#4489e4',
        indigo: '#33b0e0',
        purple: '#716cb0',
        pink: '#f24f7c',
        red: '#d03f3f',
        orange: '#f7931e',
        yellow: '#f9c851',
        green: '#22c55e',
        teal: '#0891b2',
        cyan: '#06b6d4',
    };
    return colorMap[color];
}

function getThemeColors(config: ThemeConfig) {
    // This would ideally read from CSS custom properties at runtime
    // For now, return the static color mappings
    return {
        // Velonic Brand Colors
        blue: '#4489e4',
        indigo: '#33b0e0',
        purple: '#716cb0',
        pink: '#f24f7c',
        red: '#d03f3f',
        orange: '#f7931e',
        yellow: '#f9c851',
        green: '#22c55e',
        teal: '#0891b2',
        cyan: '#06b6d4',

        // Grayscale
        gray: {
            '50': '#f9fafb',
            '100': '#f3f4f6',
            '200': '#e5e7eb',
            '300': '#d1d5db',
            '400': '#9ca3af',
            '500': '#6b7280',
            '600': '#4b5563',
            '700': '#374151',
            '800': '#1f2937',
            '900': '#111827',
            '950': '#030712',
        },

        // Semantic colors based on current config
        primary: getVelonicColorValue(config.primaryColor),
        secondary: config.mode === 'light' ? '#f3f4f6' : '#374151',
        success: '#4489e4',
        info: '#33b0e0',
        warning: '#f9c851',
        destructive: '#d03f3f',
        muted: config.mode === 'light' ? '#f3f4f6' : '#374151',
        accent: config.mode === 'light' ? '#f3f4f6' : '#374151',

        // Layout colors
        background: config.mode === 'light' ? '#ffffff' : '#111827',
        foreground: config.mode === 'light' ? '#111827' : '#f3f4f6',
        card: config.mode === 'light' ? '#ffffff' : '#1f2937',
        cardForeground: config.mode === 'light' ? '#111827' : '#f3f4f6',
        popover: config.mode === 'light' ? '#ffffff' : '#1f2937',
        popoverForeground: config.mode === 'light' ? '#111827' : '#f3f4f6',
        border: config.mode === 'light' ? '#e5e7eb' : '#374151',
        input: config.mode === 'light' ? '#e5e7eb' : '#374151',
        ring: config.mode === 'light' ? '#d1d5db' : '#4b5563',
    };
}

function getLayoutDimensions() {
    return {
        sidebarWidth: 240,
        sidebarWidthMd: 160,
        sidebarWidthSm: 70,
        topbarHeight: 70,
        logoHeight: 24,
    };
}

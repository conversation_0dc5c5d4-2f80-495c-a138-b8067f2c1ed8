<?php

namespace App;

use App\Models\Tenant;
use Illuminate\Database\Eloquent\Model;

abstract class TenantModel extends Model
{
    /**
     * Boot the model.
     */
    protected static function booted(): void
    {
        // Add the tenant scope to all queries
        static::addGlobalScope(new TenantScope);

        // Automatically set tenant_id when creating new records
        static::creating(function ($model) {
            if (app()->bound('tenant') && !$model->tenant_id) {
                $tenant = app('tenant');
                if ($tenant && $tenant->id) {
                    $model->tenant_id = $tenant->id;
                }
            }
        });
    }

    /**
     * Get the tenant that owns this model.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope query to exclude tenant filtering (for super admin use).
     */
    public function scopeWithAllTenants($query)
    {
        return $query->withoutGlobalScope(TenantScope::class);
    }
}

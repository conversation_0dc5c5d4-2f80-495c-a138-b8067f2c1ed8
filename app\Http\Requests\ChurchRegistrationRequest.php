<?php

namespace App\Http\Requests;

use App\Models\Tenant;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ChurchRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Public registration is allowed
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'church_name' => ['required', 'string', 'max:255'],
            'admin_name' => ['required', 'string', 'max:255'],
            'admin_email' => ['required', 'string', 'email', 'max:255'],
            'admin_password' => ['required', 'string', 'min:8', 'confirmed'],
            'admin_password_confirmation' => ['required', 'string', 'min:8'],
            'slug' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique(Tenant::class, 'slug')
            ],
            'terms_accepted' => ['required', 'accepted'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'church_name.required' => 'Church name is required.',
            'admin_name.required' => 'Administrator name is required.',
            'admin_email.required' => 'Administrator email is required.',
            'admin_email.email' => 'Please provide a valid email address.',
            'admin_password.required' => 'Password is required.',
            'admin_password.min' => 'Password must be at least 8 characters.',
            'admin_password.confirmed' => 'Password confirmation does not match.',
            'slug.unique' => 'This church URL is already taken. Please choose another.',
            'slug.regex' => 'Church URL can only contain lowercase letters, numbers, and hyphens.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'church_name' => 'church name',
            'admin_name' => 'administrator name',
            'admin_email' => 'administrator email',
            'admin_password' => 'password',
            'admin_password_confirmation' => 'password confirmation',
            'slug' => 'church URL',
            'terms_accepted' => 'terms and conditions',
        ];
    }
}

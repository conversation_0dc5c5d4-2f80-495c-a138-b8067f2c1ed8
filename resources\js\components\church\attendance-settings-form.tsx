import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { type AttendanceSettingsFormProps } from '@/types/church-settings';
import { Users } from 'lucide-react';

export function AttendanceSettingsForm({ formData, onNestedChange }: AttendanceSettingsFormProps) {
    return (
        <Card className="p-6">
            <div className="mb-6 flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold">Attendance Settings</h2>
            </div>

            <div className="space-y-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Attendance Preferences</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="auto_checkin"
                                checked={formData.attendance_preferences?.auto_checkin || false}
                                onChange={(e) => onNestedChange('attendance_preferences', 'auto_checkin', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="auto_checkin">Enable Auto Check-in</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="attendance_notifications"
                                checked={formData.attendance_preferences?.notification_enabled || false}
                                onChange={(e) =>
                                    onNestedChange('attendance_preferences', 'notification_enabled', e.target.checked)
                                }
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="attendance_notifications">Attendance Notifications</Label>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="late_threshold">Late Threshold (minutes)</Label>
                            <Input
                                id="late_threshold"
                                type="number"
                                min="0"
                                value={formData.attendance_preferences?.late_threshold || 30}
                                onChange={(e) =>
                                    onNestedChange('attendance_preferences', 'late_threshold', parseInt(e.target.value))
                                }
                            />
                        </div>
                    </div>
                </div>

                <Separator />

                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Manual Check-in Settings</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="manual_checkin_enabled"
                                checked={formData.manual_checkin_settings?.enabled || false}
                                onChange={(e) => onNestedChange('manual_checkin_settings', 'enabled', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="manual_checkin_enabled">Enable Manual Check-in</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="admin_only_checkin"
                                checked={formData.manual_checkin_settings?.admin_only || false}
                                onChange={(e) => onNestedChange('manual_checkin_settings', 'admin_only', e.target.checked)}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="admin_only_checkin">Admin Only Access</Label>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="checkin_window">Check-in Time Window (minutes)</Label>
                            <Input
                                id="checkin_window"
                                type="number"
                                min="0"
                                value={formData.manual_checkin_settings?.time_window || 120}
                                onChange={(e) =>
                                    onNestedChange('manual_checkin_settings', 'time_window', parseInt(e.target.value))
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
}

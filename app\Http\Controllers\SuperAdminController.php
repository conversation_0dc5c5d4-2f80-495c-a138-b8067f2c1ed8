<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SuperAdminController extends Controller
{
    /**
     * Show the super admin dashboard.
     */
    public function dashboard(): RedirectResponse
    {
        // Super admin uses the same dashboard as regular users
        return redirect()->route('dashboard');
    }

    /**
     * List all tenants.
     */
    public function tenants(Request $request)
    {
        $query = Tenant::query()->with(['users' => function ($query) {
            $query->take(3); // Get first 3 users for preview
        }]);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('slug', 'like', "%{$search}%")
                    ->orWhere('domain', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $tenants = $query->paginate(20);

        return Inertia::render('SuperAdmin/Tenants', [
            'tenants' => $tenants,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show tenant details.
     */
    public function showTenant(Tenant $tenant)
    {
        $tenant->load(['users', 'churchSettings', 'services']);

        $stats = [
            'total_users' => $tenant->users()->count(),
            'total_services' => $tenant->services()->count(),
            'total_attendance' => $tenant->attendanceRecords()->count(),
        ];

        return Inertia::render('SuperAdmin/TenantDetails', [
            'tenant' => $tenant,
            'stats' => $stats,
        ]);
    }

    /**
     * Update tenant status.
     */
    public function updateTenantStatus(Request $request, Tenant $tenant)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $tenant->update(['status' => $request->status]);

        return back()->with('success', 'Tenant status updated successfully.');
    }

    /**
     * Switch to a specific tenant context (for impersonation).
     */
    public function switchToTenant(Tenant $tenant)
    {
        if (!$tenant->isActive()) {
            return back()->withErrors(['error' => 'Cannot switch to inactive tenant.']);
        }

        // Store the original super admin context
        session(['super_admin_context' => true, 'original_user_id' => auth()->id()]);

        // Set tenant context
        app()->instance('tenant', $tenant);

        // Redirect to tenant dashboard
        $tenantUrl = $this->getTenantUrl($tenant);
        return redirect()->to($tenantUrl . '/dashboard');
    }

    /**
     * Return to super admin context.
     */
    public function returnToSuperAdmin()
    {
        session()->forget(['super_admin_context', 'original_user_id']);
        app()->forgetInstance('tenant');

        return redirect()->route('super-admin.dashboard');
    }

    /**
     * Get the full tenant URL.
     */
    protected function getTenantUrl(Tenant $tenant): string
    {
        $baseUrl = config('app.url');
        $parsedUrl = parse_url($baseUrl);

        // For development, use path-based routing
        if (app()->environment('local')) {
            return $baseUrl . '/tenant/' . $tenant->slug;
        }

        // For production, use subdomain
        return $parsedUrl['scheme'] . '://' . $tenant->slug . '.' . $parsedUrl['host'];
    }
}

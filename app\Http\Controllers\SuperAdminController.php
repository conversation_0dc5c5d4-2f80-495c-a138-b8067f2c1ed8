<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SuperAdminController extends Controller
{
    /**
     * Show the super admin dashboard.
     */
    public function dashboard(): RedirectResponse
    {
        // Super admins use the same unified dashboard as regular users
        return redirect()->route('dashboard');
    }

    /**
     * List all churches.
     */
    public function churches(Request $request)
    {
        $query = Tenant::query()->with(['users' => function ($query) {
            $query->take(3); // Get first 3 users for preview
        }]);

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('slug', 'like', "%{$search}%")
                    ->orWhere('domain', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $churches = $query->paginate(20);

        return Inertia::render('Churches/Index', [
            'churches' => $churches,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show church details.
     */
    public function showChurch(Tenant $church)
    {
        $church->load(['users', 'churchSettings', 'services']);

        $stats = [
            'total_users' => $church->users()->count(),
            'total_services' => $church->services()->count(),
            'total_attendance' => $church->attendanceRecords()->count(),
        ];

        return Inertia::render('Churches/Show', [
            'church' => $church,
            'stats' => $stats,
        ]);
    }

    /**
     * Update church status.
     */
    public function updateChurchStatus(Request $request, Tenant $church)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $church->update(['status' => $request->status]);

        return back()->with('success', 'Church status updated successfully.');
    }

    /**
     * Switch to a specific church context (for impersonation).
     */
    public function switchToChurch(Tenant $church)
    {
        if (!$church->isActive()) {
            return back()->withErrors(['error' => 'Cannot switch to inactive church.']);
        }

        // Store the original super admin context
        session(['super_admin_context' => true, 'original_user_id' => auth()->id()]);

        // Set church context
        app()->instance('tenant', $church);

        // Redirect to church dashboard
        $churchUrl = $this->getTenantUrl($church);
        return redirect()->to($churchUrl . '/dashboard');
    }

    /**
     * Return to super admin context.
     */
    public function returnToSuperAdmin()
    {
        session()->forget(['super_admin_context', 'original_user_id']);
        app()->forgetInstance('tenant');

        return redirect()->route('super-admin.dashboard');
    }

    /**
     * Get the full tenant URL.
     */
    protected function getTenantUrl(Tenant $tenant): string
    {
        $baseUrl = config('app.url');
        $parsedUrl = parse_url($baseUrl);

        // For development, use path-based routing
        if (app()->environment('local')) {
            return $baseUrl . '/tenant/' . $tenant->slug;
        }

        // For production, use subdomain
        return $parsedUrl['scheme'] . '://' . $tenant->slug . '.' . $parsedUrl['host'];
    }
}

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface AttendanceLineChartProps {
    title: string;
    description?: string;
    data: Array<{
        name: string;
        value: number;
        [key: string]: any;
    }>;
    height?: number;
    color?: string;
    loading?: boolean;
    emptyMessage?: string;
    showDots?: boolean;
    strokeWidth?: number;
}

export function AttendanceLineChart({
    title,
    description,
    data,
    height = 320,
    color = '#3b82f6',
    loading = false,
    emptyMessage = 'No data available',
    showDots = true,
    strokeWidth = 2,
}: AttendanceLineChartProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
            <CardContent>
                {data.length > 0 ? (
                    <div style={{ height }}>
                        <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={data}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                <XAxis 
                                    dataKey="name" 
                                    stroke="#6b7280"
                                    tick={{ fontSize: 12 }}
                                />
                                <YAxis stroke="#6b7280" />
                                <Tooltip 
                                    contentStyle={{ 
                                        backgroundColor: 'white', 
                                        border: '1px solid #e5e7eb',
                                        borderRadius: '8px'
                                    }} 
                                />
                                <Line 
                                    type="monotone" 
                                    dataKey="value" 
                                    stroke={color}
                                    strokeWidth={strokeWidth}
                                    dot={showDots ? { fill: color, strokeWidth: 2, r: 4 } : false}
                                />
                            </LineChart>
                        </ResponsiveContainer>
                    </div>
                ) : (
                    <div className="flex items-center justify-center text-muted-foreground" style={{ height }}>
                        {loading ? 'Loading chart data...' : emptyMessage}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

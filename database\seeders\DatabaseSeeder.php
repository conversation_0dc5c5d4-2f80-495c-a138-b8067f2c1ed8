<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create super admin first
        $this->call(SuperAdminSeeder::class);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Seed church members
        $this->call(ChurchMembersSeeder::class);

        // Seed church settings
        $this->call(ChurchSettingsSeeder::class);

        // Seed services
        $this->call(ServicesSeeder::class);

        // Seed attendance records (this should be last as it depends on users and services)
        $this->call(AttendanceRecordsSeeder::class);
    }
}

import { MemberAttendanceReport } from '@/components/reports/member-attendance-report';
import { MonthlyTrendsReport } from '@/components/reports/monthly-trends-report';
import { ServiceAttendanceReport } from '@/components/reports/service-attendance-report';
import { WeeklyAttendanceReport } from '@/components/reports/weekly-attendance-report';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { BarChart3, Calendar, Download, FileText, TrendingUp, Users } from 'lucide-react';
import { useState } from 'react';

interface ReportsPageProps {
    stats: {
        total_services_this_year: number;
        average_attendance: number;
        highest_attendance: number;
        lowest_attendance: number;
        growth_rate: number;
        active_members: number;
        regular_attendees: number;
        new_members_this_month: number;
    };
}

interface TabConfig {
    key: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Reports',
        href: '/reports',
    },
];

const tabConfigs: TabConfig[] = [
    {
        key: 'overview',
        label: 'Overview',
        icon: BarChart3,
    },
    {
        key: 'weekly',
        label: 'Weekly',
        icon: Calendar,
    },
    {
        key: 'monthly',
        label: 'Monthly',
        icon: TrendingUp,
    },
    {
        key: 'services',
        label: 'Services',
        icon: FileText,
    },
    {
        key: 'members',
        label: 'Members',
        icon: Users,
    },
];

export default function ReportsPage({ stats }: ReportsPageProps) {
    const [activeTab, setActiveTab] = useState('overview');

    const handleExport = (reportType: string, format: 'pdf' | 'excel') => {
        // This will be implemented with actual export functionality
        console.log(`Exporting ${reportType} as ${format}`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reports" />
            
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
                        <p className="text-muted-foreground">
                            Comprehensive attendance reports and analytics for your church
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => handleExport(activeTab, 'pdf')}>
                            <Download className="mr-2 h-4 w-4" />
                            Export PDF
                        </Button>
                        <Button variant="outline" onClick={() => handleExport(activeTab, 'excel')}>
                            <Download className="mr-2 h-4 w-4" />
                            Export Excel
                        </Button>
                    </div>
                </div>

                {/* Main Content */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-5">
                        {tabConfigs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <TabsTrigger key={tab.key} value={tab.key} className="flex items-center gap-2">
                                    <Icon className="h-4 w-4" />
                                    <span className="hidden sm:inline">{tab.label}</span>
                                </TabsTrigger>
                            );
                        })}
                    </TabsList>

                    {/* Overview Tab */}
                    <TabsContent value="overview" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total Services</CardTitle>
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.total_services_this_year}</div>
                                    <p className="text-xs text-muted-foreground">This year</p>
                                </CardContent>
                            </Card>
                            
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Average Attendance</CardTitle>
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.average_attendance}</div>
                                    <p className="text-xs text-muted-foreground">Per service</p>
                                </CardContent>
                            </Card>
                            
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">+{stats.growth_rate}%</div>
                                    <p className="text-xs text-muted-foreground">Year over year</p>
                                </CardContent>
                            </Card>
                            
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Active Members</CardTitle>
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.active_members}</div>
                                    <p className="text-xs text-muted-foreground">Regular attendees: {stats.regular_attendees}</p>
                                </CardContent>
                            </Card>
                        </div>

                        <div className="grid gap-6 lg:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Attendance Summary</CardTitle>
                                    <CardDescription>Key attendance metrics for this year</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex justify-between">
                                        <span className="text-sm font-medium">Highest Attendance</span>
                                        <span className="text-sm text-muted-foreground">{stats.highest_attendance}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm font-medium">Lowest Attendance</span>
                                        <span className="text-sm text-muted-foreground">{stats.lowest_attendance}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm font-medium">New Members This Month</span>
                                        <span className="text-sm text-muted-foreground">{stats.new_members_this_month}</span>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                    <CardDescription>Generate and export reports</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <Button variant="outline" className="w-full justify-start">
                                        <FileText className="mr-2 h-4 w-4" />
                                        Generate Monthly Report
                                    </Button>
                                    <Button variant="outline" className="w-full justify-start">
                                        <BarChart3 className="mr-2 h-4 w-4" />
                                        View Attendance Trends
                                    </Button>
                                    <Button variant="outline" className="w-full justify-start">
                                        <Users className="mr-2 h-4 w-4" />
                                        Member Attendance Report
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Weekly Tab */}
                    <TabsContent value="weekly" className="space-y-6">
                        <WeeklyAttendanceReport />
                    </TabsContent>

                    {/* Monthly Tab */}
                    <TabsContent value="monthly" className="space-y-6">
                        <MonthlyTrendsReport />
                    </TabsContent>

                    {/* Services Tab */}
                    <TabsContent value="services" className="space-y-6">
                        <ServiceAttendanceReport />
                    </TabsContent>

                    {/* Members Tab */}
                    <TabsContent value="members" className="space-y-6">
                        <MemberAttendanceReport />
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}

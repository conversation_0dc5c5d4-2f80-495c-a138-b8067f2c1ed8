import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { ArrowRight, BarChart3, Church, Clock, Mail, Phone, Shield, Star, Users } from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    const features = [
        {
            icon: Users,
            title: 'Member Management',
            description: 'Effortlessly manage your congregation with comprehensive member profiles, contact information, and engagement tracking.',
        },
        {
            icon: BarChart3,
            title: 'Attendance Tracking',
            description:
                "Monitor service attendance with real-time check-ins, automated reports, and insightful analytics to understand your community's growth.",
        },
        {
            icon: Clock,
            title: 'Event Scheduling',
            description: 'Organize services, meetings, and special events with integrated calendar management and automated member notifications.',
        },
        {
            icon: Shield,
            title: 'Secure & Private',
            description: 'Your church data is protected with enterprise-grade security, ensuring member privacy and data integrity at all times.',
        },
    ];

    const testimonials = [
        {
            name: 'Pastor <PERSON>',
            church: 'Grace Community Church',
            content:
                'Flockin has transformed how we manage our congregation. The attendance tracking and member engagement features have been game-changers.',
            rating: 5,
        },
        {
            name: '<PERSON>',
            church: 'First Baptist Church',
            content: 'The ease of use and comprehensive features make <PERSON>lockin perfect for churches of any size. Highly recommended!',
            rating: 5,
        },
    ];

    const stats = [
        { number: '1000+', label: 'Churches Served' },
        { number: '50K+', label: 'Members Managed' },
        { number: '99.9%', label: 'Uptime Guarantee' },
        { number: '24/7', label: 'Support Available' },
    ];

    return (
        <>
            <Head title="Flockin - Church Management Made Simple">
                <meta
                    name="description"
                    content="Streamline your church operations with Flockin's comprehensive management platform. Track attendance, manage members, and grow your community with ease."
                />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                {/* Header */}
                <header className="relative z-10 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm dark:border-gray-700/50 dark:bg-gray-900/80">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-4">
                            <div className="flex items-center space-x-2">
                                <Church className="h-8 w-8 text-blue-600" />
                                <span className="text-2xl font-bold text-gray-900 dark:text-white">Flockin</span>
                            </div>
                            <nav className="flex items-center space-x-4">
                                {auth.user ? (
                                    <Link
                                        href={route('dashboard')}
                                        className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors duration-200 hover:bg-blue-700"
                                    >
                                        Go to Dashboard
                                    </Link>
                                ) : (
                                    <>
                                        <Link
                                            href={route('login')}
                                            className="rounded-md px-3 py-2 text-gray-700 transition-colors duration-200 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                                        >
                                            Sign In
                                        </Link>
                                        <Link
                                            href={route('church-registration')}
                                            className="inline-flex items-center rounded-lg bg-blue-600 px-6 py-2 font-medium text-white transition-colors duration-200 hover:bg-blue-700"
                                        >
                                            Start Free Trial
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Link>
                                    </>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>
                {/* Hero Section */}
                <section className="relative py-20 lg:py-32">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-6xl dark:text-white">
                                Church Management
                                <span className="block text-blue-600">Made Simple</span>
                            </h1>
                            <p className="mx-auto mb-8 max-w-3xl text-xl text-gray-600 dark:text-gray-300">
                                Streamline your church operations with our comprehensive management platform. Track attendance, manage members,
                                organize events, and grow your community with ease.
                            </p>
                            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                                <Link
                                    href={route('church-registration')}
                                    className="inline-flex items-center rounded-lg bg-blue-600 px-8 py-4 text-lg font-semibold text-white shadow-lg transition-colors duration-200 hover:bg-blue-700 hover:shadow-xl"
                                >
                                    Get Started Free
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                                <a
                                    href="#features"
                                    className="inline-flex items-center rounded-lg border-2 border-gray-300 px-8 py-4 text-lg font-semibold text-gray-700 transition-colors duration-200 hover:border-blue-600 hover:text-blue-600 dark:border-gray-600 dark:text-gray-300 dark:hover:border-blue-400 dark:hover:text-blue-400"
                                >
                                    Learn More
                                </a>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Stats Section */}
                <section className="bg-white py-16 dark:bg-gray-800">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
                            {stats.map((stat, index) => (
                                <div key={index} className="text-center">
                                    <div className="mb-2 text-3xl font-bold text-blue-600 md:text-4xl">{stat.number}</div>
                                    <div className="text-gray-600 dark:text-gray-300">{stat.label}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="bg-gray-50 py-20 dark:bg-gray-900">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Everything Your Church Needs</h2>
                            <p className="mx-auto max-w-3xl text-xl text-gray-600 dark:text-gray-300">
                                Powerful features designed specifically for churches to manage their community, track growth, and strengthen
                                connections.
                            </p>
                        </div>
                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                            {features.map((feature, index) => {
                                const IconComponent = feature.icon;
                                return (
                                    <div
                                        key={index}
                                        className="rounded-xl bg-white p-6 shadow-lg transition-shadow duration-200 hover:shadow-xl dark:bg-gray-800"
                                    >
                                        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                                            <IconComponent className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">{feature.title}</h3>
                                        <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </section>
                {/* Testimonials Section */}
                <section className="bg-white py-20 dark:bg-gray-800">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Trusted by Churches Worldwide</h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">See what church leaders are saying about Flockin</p>
                        </div>
                        <div className="grid gap-8 md:grid-cols-2">
                            {testimonials.map((testimonial, index) => (
                                <div key={index} className="rounded-xl bg-gray-50 p-8 dark:bg-gray-700">
                                    <div className="mb-4 flex">
                                        {[...Array(testimonial.rating)].map((_, i) => (
                                            <Star key={i} className="h-5 w-5 fill-current text-yellow-400" />
                                        ))}
                                    </div>
                                    <p className="mb-6 text-lg text-gray-700 dark:text-gray-300">"{testimonial.content}"</p>
                                    <div>
                                        <div className="font-semibold text-gray-900 dark:text-white">{testimonial.name}</div>
                                        <div className="text-gray-600 dark:text-gray-400">{testimonial.church}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* How It Works Section */}
                <section className="bg-gray-50 py-20 dark:bg-gray-900">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Get Started in Minutes</h2>
                            <p className="text-xl text-gray-600 dark:text-gray-300">Simple steps to transform your church management</p>
                        </div>
                        <div className="grid gap-8 md:grid-cols-3">
                            <div className="text-center">
                                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                                    <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">1</span>
                                </div>
                                <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">Register Your Church</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Create your church account in under 2 minutes with our simple registration process.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                                    <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">2</span>
                                </div>
                                <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">Import Your Data</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Easily import your existing member data or start fresh with our intuitive setup wizard.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                                    <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">3</span>
                                </div>
                                <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">Start Managing</h3>
                                <p className="text-gray-600 dark:text-gray-300">
                                    Begin tracking attendance, managing events, and growing your community immediately.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
                {/* CTA Section */}
                <section className="bg-blue-600 py-20 dark:bg-blue-700">
                    <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
                        <h2 className="mb-4 text-3xl font-bold text-white md:text-4xl">Ready to Transform Your Church Management?</h2>
                        <p className="mx-auto mb-8 max-w-2xl text-xl text-blue-100">
                            Join thousands of churches already using Flockin to build stronger communities and streamline their operations.
                        </p>
                        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                            <Link
                                href={route('church-registration')}
                                className="inline-flex items-center rounded-lg bg-white px-8 py-4 text-lg font-semibold text-blue-600 shadow-lg transition-colors duration-200 hover:bg-gray-100 hover:shadow-xl"
                            >
                                Start Your Free Trial
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                            <a
                                href="mailto:<EMAIL>"
                                className="inline-flex items-center rounded-lg border-2 border-white px-8 py-4 text-lg font-semibold text-white transition-colors duration-200 hover:bg-white hover:text-blue-600"
                            >
                                Contact Sales
                            </a>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 py-16 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 md:grid-cols-4">
                            <div className="md:col-span-2">
                                <div className="mb-4 flex items-center space-x-2">
                                    <Church className="h-8 w-8 text-blue-400" />
                                    <span className="text-2xl font-bold">Flockin</span>
                                </div>
                                <p className="mb-6 max-w-md text-gray-400">
                                    Empowering churches worldwide with comprehensive management tools to build stronger communities and streamline
                                    operations.
                                </p>
                                <div className="flex space-x-4">
                                    <a href="mailto:<EMAIL>" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                        <Mail className="h-6 w-6" />
                                    </a>
                                    <a href="tel:******-0123" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                        <Phone className="h-6 w-6" />
                                    </a>
                                </div>
                            </div>
                            <div>
                                <h3 className="mb-4 text-lg font-semibold">Product</h3>
                                <ul className="space-y-2">
                                    <li>
                                        <a href="#features" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Features
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Pricing
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Security
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Integrations
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="mb-4 text-lg font-semibold">Support</h3>
                                <ul className="space-y-2">
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Help Center
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Contact Us
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Training
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" className="text-gray-400 transition-colors duration-200 hover:text-white">
                                            Community
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div className="mt-12 flex flex-col items-center justify-between border-t border-gray-800 pt-8 md:flex-row">
                            <p className="text-sm text-gray-400">© 2024 Flockin. All rights reserved.</p>
                            <div className="mt-4 flex space-x-6 md:mt-0">
                                <a href="#" className="text-sm text-gray-400 transition-colors duration-200 hover:text-white">
                                    Privacy Policy
                                </a>
                                <a href="#" className="text-sm text-gray-400 transition-colors duration-200 hover:text-white">
                                    Terms of Service
                                </a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}

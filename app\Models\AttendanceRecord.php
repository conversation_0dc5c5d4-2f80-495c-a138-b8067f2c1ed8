<?php

namespace App\Models;

use App\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AttendanceRecord extends TenantModel
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'service_id',
        'service_date',
        'check_in_time',
        'check_out_time',
        'status',
        'location_lat',
        'location_lng',
        'check_in_method',
        'notes',
        'is_volunteer',
        'department',
    ];

    protected function casts(): array
    {
        return [
            'service_date' => 'date',
            'check_in_time' => 'datetime',
            'check_out_time' => 'datetime',
            'location_lat' => 'decimal:8',
            'location_lng' => 'decimal:8',
            'is_volunteer' => 'boolean',
        ];
    }

    /**
     * Get the user who attended.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service that was attended.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Scope for present attendees.
     */
    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    /**
     * Scope for late attendees.
     */
    public function scopeLate($query)
    {
        return $query->where('status', 'late');
    }

    /**
     * Scope for absent attendees.
     */
    public function scopeAbsent($query)
    {
        return $query->where('status', 'absent');
    }

    /**
     * Scope for volunteers.
     */
    public function scopeVolunteers($query)
    {
        return $query->where('is_volunteer', true);
    }

    /**
     * Scope for a specific date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('service_date', [$startDate, $endDate]);
    }

    /**
     * Scope for a specific service type.
     */
    public function scopeByServiceType($query, string $serviceType)
    {
        return $query->whereHas('service', function ($q) use ($serviceType) {
            $q->where('service_type', $serviceType);
        });
    }
}

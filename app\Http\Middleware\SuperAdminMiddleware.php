<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user || !$user->is_super_admin) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthorized. Super admin access required.'], 403);
            }

            abort(403, 'Unauthorized. Super admin access required.');
        }

        // Enable tenant bypass for super admin operations
        app()->instance('tenant.bypass', true);

        return $next($request);
    }
}

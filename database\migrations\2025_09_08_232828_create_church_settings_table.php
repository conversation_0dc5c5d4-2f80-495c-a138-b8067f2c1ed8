<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('church_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();

            // Church Information
            $table->string('church_name')->nullable();
            $table->text('church_address')->nullable();
            $table->string('church_phone')->nullable();
            $table->string('church_email')->nullable();
            $table->string('church_website')->nullable();
            $table->json('social_media_links')->nullable();
            $table->text('church_description')->nullable();
            $table->text('mission_statement')->nullable();
            $table->json('service_times')->nullable();
            $table->json('leadership_info')->nullable();

            // Geolocation & Geofence
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->integer('geofence_radius')->default(100); // in meters

            // Attendance Settings
            $table->json('attendance_preferences')->nullable();
            $table->json('manual_checkin_settings')->nullable();
            $table->json('notification_settings')->nullable();
            $table->json('attendance_report_configs')->nullable();

            // System Settings
            $table->json('user_roles_permissions')->nullable();
            $table->json('notification_preferences')->nullable();
            $table->json('backup_settings')->nullable();
            $table->json('privacy_security_configs')->nullable();

            // Integration Settings
            $table->json('mobile_app_config')->nullable();
            $table->json('third_party_integrations')->nullable();
            $table->json('api_keys')->nullable();

            $table->timestamps();

            // Index for performance
            $table->index('tenant_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('church_settings');
    }
};

import { useEffect, useRef, useState } from 'react';

interface ChurchMapComponentProps {
    latitude: number;
    longitude: number;
    geofenceRadius: number;
    onLocationUpdate: (lat: number, lng: number) => void;
}

export function ChurchMapComponent({ latitude, longitude, geofenceRadius, onLocationUpdate }: ChurchMapComponentProps) {
    const mapRef = useRef<HTMLDivElement>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // For now, we'll create a placeholder map
        // In a real implementation, this would use Leaflet
        setIsLoading(false);
    }, []);

    const handleMapClick = (e: React.MouseEvent<HTMLDivElement>) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Mock coordinate calculation (this would be real in Leaflet)
        const mockLat = latitude + (y - 200) / 1000;
        const mockLng = longitude + (x - 300) / 1000;
        
        onLocationUpdate(mockLat, mockLng);
    };

    if (error) {
        return (
            <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
                <div className="text-center space-y-2">
                    <p className="text-red-600 font-medium">Map Error</p>
                    <p className="text-sm text-gray-600">{error}</p>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
                <div className="text-center space-y-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="text-sm text-gray-600">Loading map...</p>
                </div>
            </div>
        );
    }

    return (
        <div 
            ref={mapRef} 
            className="w-full h-full bg-gray-100 rounded-lg cursor-crosshair relative overflow-hidden"
            onClick={handleMapClick}
        >
            {/* Map placeholder with visual representation */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
                {/* Grid pattern to simulate map */}
                <div className="absolute inset-0 opacity-20">
                    {Array.from({ length: 20 }).map((_, i) => (
                        <div key={`h-${i}`} className="absolute border-t border-gray-300" style={{ top: `${i * 5}%`, width: '100%' }} />
                    ))}
                    {Array.from({ length: 20 }).map((_, i) => (
                        <div key={`v-${i}`} className="absolute border-l border-gray-300" style={{ left: `${i * 5}%`, height: '100%' }} />
                    ))}
                </div>
                
                {/* Church marker */}
                {latitude && longitude && (
                    <div 
                        className="absolute w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow-lg transform -translate-x-1/2 -translate-y-1/2"
                        style={{ 
                            left: '50%', 
                            top: '50%' 
                        }}
                    >
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-white rounded-full"></div>
                    </div>
                )}
                
                {/* Geofence circle */}
                {latitude && longitude && geofenceRadius && (
                    <div 
                        className="absolute border-2 border-primary border-opacity-60 bg-primary bg-opacity-10 rounded-full transform -translate-x-1/2 -translate-y-1/2"
                        style={{ 
                            left: '50%', 
                            top: '50%',
                            width: `${Math.min(geofenceRadius / 2, 200)}px`,
                            height: `${Math.min(geofenceRadius / 2, 200)}px`
                        }}
                    />
                )}
                
                {/* Instructions */}
                <div className="absolute bottom-4 left-4 bg-white p-2 rounded shadow-sm text-xs text-gray-600">
                    Click to set church location
                </div>
                
                {/* Coordinates display */}
                {latitude && longitude && (
                    <div className="absolute top-4 right-4 bg-white p-2 rounded shadow-sm text-xs">
                        <div>Lat: {latitude.toFixed(6)}</div>
                        <div>Lng: {longitude.toFixed(6)}</div>
                        <div>Radius: {geofenceRadius}m</div>
                    </div>
                )}
            </div>
        </div>
    );
}

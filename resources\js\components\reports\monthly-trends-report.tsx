import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type MonthlyTrendData } from '@/types/reports';
import { Calendar, TrendingDown, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Area, AreaChart, CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

interface MonthlyTrendsReportProps {
    initialData?: MonthlyTrendData[];
}

export function MonthlyTrendsReport({ initialData = [] }: MonthlyTrendsReportProps) {
    const [data, setData] = useState<MonthlyTrendData[]>(initialData);
    const [loading, setLoading] = useState(false);
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());

    const fetchMonthlyData = async () => {
        setLoading(true);
        try {
            const response = await fetch(`/reports/monthly-trends?year=${selectedYear}`);
            const result = await response.json();
            setData(result.data || []);
        } catch (error) {
            console.error('Error fetching monthly trends data:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchMonthlyData();
    }, [selectedYear]);

    const totalAttendance = data.reduce((sum, item) => sum + item.attendance, 0);
    const averageAttendance = data.length > 0 ? Math.round(totalAttendance / data.length) : 0;
    const highestMonth = data.reduce((max, item) => item.attendance > max.attendance ? item : max, data[0] || { month: '', attendance: 0 });
    const lowestMonth = data.reduce((min, item) => item.attendance < min.attendance ? item : min, data[0] || { month: '', attendance: 0 });
    const averageGrowth = data.length > 0 ? data.reduce((sum, item) => sum + item.growth, 0) / data.length : 0;

    const currentYear = new Date().getFullYear();
    const yearOptions = Array.from({ length: 5 }, (_, i) => currentYear - i);

    return (
        <div className="space-y-6">
            {/* Year Filter */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Monthly Trends Analysis
                    </CardTitle>
                    <CardDescription>View monthly attendance trends and growth patterns</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center gap-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Year</label>
                            <Select value={selectedYear} onValueChange={setSelectedYear}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    {yearOptions.map(year => (
                                        <SelectItem key={year} value={year.toString()}>
                                            {year}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <Button onClick={fetchMonthlyData} disabled={loading} variant="outline">
                            {loading ? 'Loading...' : 'Refresh Data'}
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Attendance</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalAttendance.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">For {selectedYear}</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Monthly Average</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{averageAttendance}</div>
                        <p className="text-xs text-muted-foreground">Per month</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Average Growth</CardTitle>
                        {averageGrowth >= 0 ? (
                            <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                            <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                    </CardHeader>
                    <CardContent>
                        <div className={`text-2xl font-bold ${averageGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {averageGrowth >= 0 ? '+' : ''}{averageGrowth.toFixed(1)}%
                        </div>
                        <p className="text-xs text-muted-foreground">Month over month</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Best Month</CardTitle>
                        <TrendingUp className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{highestMonth?.attendance || 0}</div>
                        <p className="text-xs text-muted-foreground">{highestMonth?.month || 'N/A'}</p>
                    </CardContent>
                </Card>
            </div>

            {/* Attendance Trend Chart */}
            <Card>
                <CardHeader>
                    <CardTitle>Monthly Attendance Trend</CardTitle>
                    <CardDescription>Attendance vs target throughout the year</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                                <AreaChart data={data}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis 
                                        dataKey="month" 
                                        stroke="#6b7280"
                                        tick={{ fontSize: 12 }}
                                    />
                                    <YAxis stroke="#6b7280" />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: 'white', 
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '8px'
                                        }} 
                                    />
                                    <Area 
                                        type="monotone" 
                                        dataKey="target" 
                                        stackId="1" 
                                        stroke="#e5e7eb" 
                                        fill="#f3f4f6" 
                                        name="Target"
                                    />
                                    <Area 
                                        type="monotone" 
                                        dataKey="attendance" 
                                        stackId="2" 
                                        stroke="#3b82f6" 
                                        fill="#3b82f6" 
                                        fillOpacity={0.6}
                                        name="Attendance"
                                    />
                                </AreaChart>
                            </ResponsiveContainer>
                        </div>
                    ) : (
                        <div className="flex h-80 items-center justify-center text-muted-foreground">
                            {loading ? 'Loading monthly trends...' : 'No data available for the selected year.'}
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Growth Rate Chart */}
            <Card>
                <CardHeader>
                    <CardTitle>Monthly Growth Rate</CardTitle>
                    <CardDescription>Month-over-month growth percentage</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="h-64">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={data}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis 
                                        dataKey="month" 
                                        stroke="#6b7280"
                                        tick={{ fontSize: 12 }}
                                    />
                                    <YAxis stroke="#6b7280" />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: 'white', 
                                            border: '1px solid #e5e7eb',
                                            borderRadius: '8px'
                                        }}
                                        formatter={(value: number) => [`${value.toFixed(1)}%`, 'Growth Rate']}
                                    />
                                    <Line 
                                        type="monotone" 
                                        dataKey="growth" 
                                        stroke="#10b981" 
                                        strokeWidth={2}
                                        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                                        name="Growth Rate"
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </div>
                    ) : (
                        <div className="flex h-64 items-center justify-center text-muted-foreground">
                            No growth data available.
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Monthly Data Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Monthly Breakdown</CardTitle>
                    <CardDescription>Detailed monthly attendance and growth data</CardDescription>
                </CardHeader>
                <CardContent>
                    {data.length > 0 ? (
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">Month</th>
                                        <th className="text-right p-2">Attendance</th>
                                        <th className="text-right p-2">Target</th>
                                        <th className="text-right p-2">Difference</th>
                                        <th className="text-right p-2">Growth Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {data.map((item, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="p-2 font-medium">{item.month}</td>
                                            <td className="text-right p-2">{item.attendance.toLocaleString()}</td>
                                            <td className="text-right p-2">{item.target.toLocaleString()}</td>
                                            <td className="text-right p-2">
                                                <span className={`${
                                                    item.attendance >= item.target 
                                                        ? 'text-green-600' 
                                                        : 'text-red-600'
                                                }`}>
                                                    {item.attendance >= item.target ? '+' : ''}
                                                    {(item.attendance - item.target).toLocaleString()}
                                                </span>
                                            </td>
                                            <td className="text-right p-2">
                                                <span className={`px-2 py-1 rounded text-xs ${
                                                    item.growth >= 0 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {item.growth >= 0 ? '+' : ''}{item.growth.toFixed(1)}%
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground py-8">
                            No monthly data available for {selectedYear}.
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}

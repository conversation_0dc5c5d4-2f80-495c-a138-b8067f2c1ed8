<?php

namespace Tests\Feature;

use App\Models\ChurchSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChurchSettingsTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_view_church_settings(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/church-settings');

        $response->assertStatus(200);
        $response->assertInertia(fn($page) => $page->component('church-settings'));
    }

    public function test_unauthenticated_user_cannot_view_church_settings(): void
    {
        $response = $this->get('/church-settings');

        $response->assertRedirect('/login');
    }

    public function test_church_settings_can_be_updated(): void
    {
        $user = User::factory()->create();

        $data = [
            'church_name' => 'Test Church',
            'church_address' => '123 Test Street',
            'church_phone' => '****** 567 8901',
            'church_email' => '<EMAIL>',
            'church_website' => 'https://www.testchurch.com',
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'geofence_radius' => 150,
        ];

        $response = $this->actingAs($user)->put('/church-settings', $data);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Church settings updated successfully.');

        $this->assertDatabaseHas('church_settings', [
            'church_name' => 'Test Church',
            'church_email' => '<EMAIL>',
        ]);
    }

    public function test_church_settings_validation(): void
    {
        $user = User::factory()->create();

        $data = [
            'church_email' => 'invalid-email',
            'church_website' => 'not-a-url',
            'latitude' => 100, // Invalid latitude
            'longitude' => 200, // Invalid longitude
            'geofence_radius' => 5, // Too small
        ];

        $response = $this->actingAs($user)->put('/church-settings', $data);

        $response->assertSessionHasErrors([
            'church_email',
            'church_website',
            'latitude',
            'longitude',
            'geofence_radius',
        ]);
    }

    public function test_church_settings_model_current_method(): void
    {
        // Test that current() creates a new setting if none exists
        $settings = ChurchSetting::current();
        $this->assertInstanceOf(ChurchSetting::class, $settings);
        $this->assertEquals(1, $settings->id);

        // Test that current() returns existing setting
        $existingSettings = ChurchSetting::current();
        $this->assertEquals($settings->id, $existingSettings->id);
    }

    public function test_church_settings_json_casting(): void
    {
        $settings = ChurchSetting::create([
            'church_name' => 'Test Church',
            'social_media_links' => [
                'facebook' => 'https://facebook.com/test',
                'twitter' => 'https://twitter.com/test',
            ],
            'service_times' => [
                ['day' => 'Sunday', 'time' => '9:00 AM', 'service_type' => 'Main Service'],
            ],
        ]);

        $this->assertIsArray($settings->social_media_links);
        $this->assertIsArray($settings->service_times);
        $this->assertEquals('https://facebook.com/test', $settings->social_media_links['facebook']);
        $this->assertEquals('Sunday', $settings->service_times[0]['day']);
    }
}

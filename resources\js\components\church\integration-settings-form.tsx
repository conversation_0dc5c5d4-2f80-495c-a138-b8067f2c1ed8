import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { type IntegrationSettingsFormProps } from '@/types/church-settings';
import { Smartphone } from 'lucide-react';

export function IntegrationSettingsForm({ formData, onNestedChange }: IntegrationSettingsFormProps) {
    return (
        <Card className="p-6">
            <div className="mb-6 flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-primary" />
                <h2 className="text-xl font-semibold">Integration Settings</h2>
            </div>

            <div className="space-y-6">
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Mobile App Configuration</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                            <Label htmlFor="app_name">App Name</Label>
                            <Input
                                id="app_name"
                                value={formData.mobile_app_config?.app_name || ''}
                                onChange={(e) => onNestedChange('mobile_app_config', 'app_name', e.target.value)}
                                placeholder="Church App"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="theme_color">Theme Color</Label>
                            <Input
                                id="theme_color"
                                type="color"
                                value={formData.mobile_app_config?.theme_color || '#34B0E0'}
                                onChange={(e) => onNestedChange('mobile_app_config', 'theme_color', e.target.value)}
                            />
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="app_notifications"
                                checked={formData.mobile_app_config?.notification_enabled || false}
                                onChange={(e) =>
                                    onNestedChange('mobile_app_config', 'notification_enabled', e.target.checked)
                                }
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="app_notifications">Enable App Notifications</Label>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
}

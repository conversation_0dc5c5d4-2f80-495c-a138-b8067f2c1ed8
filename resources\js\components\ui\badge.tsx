import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import type { VelonicBrandColor, SemanticColor } from "@/types/theme"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow,background-color] overflow-auto",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
        destructive:
          "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline:
          "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",
        soft:
          "border-transparent bg-primary/10 text-primary [a&]:hover:bg-primary/20",
        // Velonic outline variants
        "outline-primary": "border-2 border-primary text-primary bg-transparent [a&]:hover:bg-primary/5",
        "outline-secondary": "border-2 border-secondary text-secondary-foreground bg-transparent [a&]:hover:bg-secondary/5",
        "outline-success": "border-2 border-blue text-blue bg-transparent [a&]:hover:bg-blue/5",
        "outline-info": "border-2 border-indigo text-indigo bg-transparent [a&]:hover:bg-indigo/5",
        "outline-warning": "border-2 border-yellow text-yellow bg-transparent [a&]:hover:bg-yellow/5",
        "outline-danger": "border-2 border-destructive text-destructive bg-transparent [a&]:hover:bg-destructive/5",
        // Velonic brand color variants
        blue: "border-transparent bg-blue text-white [a&]:hover:bg-blue/90",
        indigo: "border-transparent bg-indigo text-white [a&]:hover:bg-indigo/90",
        purple: "border-transparent bg-purple text-white [a&]:hover:bg-purple/90",
        pink: "border-transparent bg-pink text-white [a&]:hover:bg-pink/90",
        red: "border-transparent bg-red text-white [a&]:hover:bg-red/90",
        orange: "border-transparent bg-orange text-white [a&]:hover:bg-orange/90",
        yellow: "border-transparent bg-yellow text-gray-900 [a&]:hover:bg-yellow/90",
        green: "border-transparent bg-green text-white [a&]:hover:bg-green/90",
        teal: "border-transparent bg-teal text-white [a&]:hover:bg-teal/90",
        cyan: "border-transparent bg-cyan text-white [a&]:hover:bg-cyan/90",
        // Velonic soft variants
        "soft-primary": "border-transparent bg-primary/10 text-primary [a&]:hover:bg-primary/20",
        "soft-secondary": "border-transparent bg-secondary/10 text-secondary-foreground [a&]:hover:bg-secondary/20",
        "soft-success": "border-transparent bg-blue-subtle text-blue [a&]:hover:bg-blue/20",
        "soft-info": "border-transparent bg-indigo-subtle text-indigo [a&]:hover:bg-indigo/20",
        "soft-warning": "border-transparent bg-yellow-subtle text-yellow [a&]:hover:bg-yellow/20",
        "soft-danger": "border-transparent bg-destructive/10 text-destructive [a&]:hover:bg-destructive/20",
        "soft-blue": "border-transparent bg-blue-subtle text-blue [a&]:hover:bg-blue/20",
        "soft-indigo": "border-transparent bg-indigo-subtle text-indigo [a&]:hover:bg-indigo/20",
        "soft-purple": "border-transparent bg-purple-subtle text-purple [a&]:hover:bg-purple/20",
        "soft-pink": "border-transparent bg-pink-subtle text-pink [a&]:hover:bg-pink/20",
        "soft-green": "border-transparent bg-green-subtle text-green [a&]:hover:bg-green/20",
        "soft-orange": "border-transparent bg-orange-subtle text-orange [a&]:hover:bg-orange/20",
        "soft-teal": "border-transparent bg-teal-subtle text-teal [a&]:hover:bg-teal/20",
      },
      size: {
        default: "px-2 py-0.5 text-xs",
        sm: "px-1.5 py-0 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

interface BadgeProps extends 
  React.ComponentProps<"span">,
  VariantProps<typeof badgeVariants> {
  asChild?: boolean
  color?: VelonicBrandColor | SemanticColor
  dot?: boolean
  removable?: boolean
  onRemove?: () => void
}

function Badge({
  className,
  variant,
  size,
  color,
  dot,
  removable,
  onRemove,
  children,
  asChild = false,
  ...props
}: BadgeProps) {
  const Comp = asChild ? Slot : "span"

  // Handle dynamic color variants
  const colorVariant = React.useMemo(() => {
    if (color && !variant) {
      return color as any;
    }
    if (color && variant === 'soft') {
      return `soft-${color}` as any;
    }
    if (color && variant === 'outline') {
      return `outline-${color}` as any;
    }
    return variant;
  }, [variant, color]);

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant: colorVariant, size }), className)}
      {...props}
    >
      {/* Dot indicator */}
      {dot && (
        <span className="h-1.5 w-1.5 rounded-full bg-current shrink-0" />
      )}
      
      {/* Badge content */}
      {children}
      
      {/* Remove button */}
      {removable && onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="ml-1 h-3 w-3 rounded-full hover:bg-current/20 flex items-center justify-center shrink-0"
          aria-label="Remove badge"
        >
          <svg className="h-2 w-2" fill="currentColor" viewBox="0 0 8 8">
            <path d="M1.5 1.5l5 5m0-5l-5 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
          </svg>
        </button>
      )}
    </Comp>
  )
}

export { Badge, badgeVariants, type BadgeProps }

<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;

class ServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Sunday Morning Worship',
                'description' => 'Main Sunday morning worship service',
                'service_type' => 'sunday_morning',
                'day_of_week' => 0, // Sunday
                'start_time' => '10:00:00',
                'end_time' => '11:30:00',
                'is_active' => true,
                'target_attendance' => 250,
                'location' => 'Main Sanctuary',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Sunday Evening Service',
                'description' => 'Sunday evening worship and fellowship',
                'service_type' => 'sunday_evening',
                'day_of_week' => 0, // Sunday
                'start_time' => '18:00:00',
                'end_time' => '19:30:00',
                'is_active' => true,
                'target_attendance' => 120,
                'location' => 'Main Sanctuary',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Wednesday Evening Prayer',
                'description' => 'Midweek prayer and Bible study',
                'service_type' => 'wednesday_evening',
                'day_of_week' => 3, // Wednesday
                'start_time' => '19:00:00',
                'end_time' => '20:30:00',
                'is_active' => true,
                'target_attendance' => 100,
                'location' => 'Fellowship Hall',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Youth Service',
                'description' => 'Weekly youth gathering and activities',
                'service_type' => 'youth',
                'day_of_week' => 5, // Friday
                'start_time' => '19:00:00',
                'end_time' => '21:00:00',
                'is_active' => true,
                'target_attendance' => 60,
                'location' => 'Youth Center',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Children\'s Church',
                'description' => 'Sunday morning service for children',
                'service_type' => 'children',
                'day_of_week' => 0, // Sunday
                'start_time' => '10:00:00',
                'end_time' => '11:00:00',
                'is_active' => true,
                'target_attendance' => 80,
                'location' => 'Children\'s Wing',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Men\'s Fellowship',
                'description' => 'Monthly men\'s breakfast and fellowship',
                'service_type' => 'fellowship',
                'day_of_week' => 6, // Saturday
                'start_time' => '08:00:00',
                'end_time' => '10:00:00',
                'is_active' => true,
                'target_attendance' => 40,
                'location' => 'Fellowship Hall',
                'recurring' => true,
                'frequency' => 'monthly',
            ],
            [
                'name' => 'Women\'s Bible Study',
                'description' => 'Weekly women\'s Bible study and prayer',
                'service_type' => 'bible_study',
                'day_of_week' => 2, // Tuesday
                'start_time' => '10:00:00',
                'end_time' => '11:30:00',
                'is_active' => true,
                'target_attendance' => 35,
                'location' => 'Conference Room',
                'recurring' => true,
                'frequency' => 'weekly',
            ],
            [
                'name' => 'Special Events',
                'description' => 'Special church events and celebrations',
                'service_type' => 'special',
                'day_of_week' => 0, // Variable
                'start_time' => '10:00:00',
                'end_time' => '12:00:00',
                'is_active' => true,
                'target_attendance' => 300,
                'location' => 'Main Sanctuary',
                'recurring' => false,
                'frequency' => 'special',
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }
    }
}

import { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2, Church } from 'lucide-react';

interface ChurchRegistrationProps {
    suggestedSlug: string;
}

interface FormData {
    church_name: string;
    admin_name: string;
    admin_email: string;
    admin_password: string;
    admin_password_confirmation: string;
    slug: string;
    terms_accepted: boolean;
}

export default function ChurchRegistration({ suggestedSlug }: ChurchRegistrationProps) {
    const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
    const [checkingSlug, setCheckingSlug] = useState(false);
    const [slugMessage, setSlugMessage] = useState('');

    const { data, setData, post, processing, errors, reset } = useForm<FormData>({
        church_name: '',
        admin_name: '',
        admin_email: '',
        admin_password: '',
        admin_password_confirmation: '',
        slug: suggestedSlug,
        terms_accepted: false,
    });

    // Check slug availability when it changes
    useEffect(() => {
        if (data.slug && data.slug.length > 2) {
            const timeoutId = setTimeout(() => {
                checkSlugAvailability(data.slug);
            }, 500);

            return () => clearTimeout(timeoutId);
        } else {
            setSlugAvailable(null);
            setSlugMessage('');
        }
    }, [data.slug]);

    const checkSlugAvailability = async (slug: string) => {
        setCheckingSlug(true);
        try {
            const response = await fetch('/church-registration/check-slug', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ slug }),
            });

            const result = await response.json();
            setSlugAvailable(result.available);
            setSlugMessage(result.message);
        } catch (error) {
            console.error('Error checking slug:', error);
        } finally {
            setCheckingSlug(false);
        }
    };

    const generateSlugFromChurchName = (churchName: string) => {
        return churchName
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    };

    const handleChurchNameChange = (value: string) => {
        setData('church_name', value);
        
        // Auto-generate slug if it's empty or matches the previous auto-generated one
        if (!data.slug || data.slug === generateSlugFromChurchName(data.church_name)) {
            const newSlug = generateSlugFromChurchName(value);
            setData('slug', newSlug);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/church-registration');
    };

    return (
        <>
            <Head title="Register Your Church - Flockin" />
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
                <Card className="w-full max-w-2xl">
                    <CardHeader className="text-center">
                        <div className="flex justify-center mb-4">
                            <Church className="h-12 w-12 text-blue-600" />
                        </div>
                        <CardTitle className="text-3xl font-bold text-gray-900">
                            Register Your Church
                        </CardTitle>
                        <CardDescription className="text-lg text-gray-600">
                            Join thousands of churches using Flockin to manage their community
                        </CardDescription>
                    </CardHeader>

                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Church Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-gray-900">Church Information</h3>
                                
                                <div>
                                    <Label htmlFor="church_name">Church Name *</Label>
                                    <Input
                                        id="church_name"
                                        type="text"
                                        value={data.church_name}
                                        onChange={(e) => handleChurchNameChange(e.target.value)}
                                        placeholder="e.g., Grace Community Church"
                                        className={errors.church_name ? 'border-red-500' : ''}
                                        required
                                    />
                                    {errors.church_name && (
                                        <p className="text-sm text-red-600 mt-1">{errors.church_name}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="slug">Church URL *</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="slug"
                                            type="text"
                                            value={data.slug}
                                            onChange={(e) => setData('slug', e.target.value)}
                                            placeholder="your-church-name"
                                            className={errors.slug ? 'border-red-500' : ''}
                                            required
                                        />
                                        <div className="flex items-center">
                                            {checkingSlug && <Loader2 className="h-4 w-4 animate-spin text-gray-500" />}
                                            {!checkingSlug && slugAvailable === true && (
                                                <CheckCircle className="h-4 w-4 text-green-500" />
                                            )}
                                            {!checkingSlug && slugAvailable === false && (
                                                <XCircle className="h-4 w-4 text-red-500" />
                                            )}
                                        </div>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Your church will be accessible at: <strong>{data.slug}.flockin.app</strong>
                                    </p>
                                    {slugMessage && (
                                        <p className={`text-sm mt-1 ${slugAvailable ? 'text-green-600' : 'text-red-600'}`}>
                                            {slugMessage}
                                        </p>
                                    )}
                                    {errors.slug && (
                                        <p className="text-sm text-red-600 mt-1">{errors.slug}</p>
                                    )}
                                </div>
                            </div>

                            {/* Administrator Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-gray-900">Administrator Information</h3>
                                
                                <div>
                                    <Label htmlFor="admin_name">Full Name *</Label>
                                    <Input
                                        id="admin_name"
                                        type="text"
                                        value={data.admin_name}
                                        onChange={(e) => setData('admin_name', e.target.value)}
                                        placeholder="John Doe"
                                        className={errors.admin_name ? 'border-red-500' : ''}
                                        required
                                    />
                                    {errors.admin_name && (
                                        <p className="text-sm text-red-600 mt-1">{errors.admin_name}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="admin_email">Email Address *</Label>
                                    <Input
                                        id="admin_email"
                                        type="email"
                                        value={data.admin_email}
                                        onChange={(e) => setData('admin_email', e.target.value)}
                                        placeholder="<EMAIL>"
                                        className={errors.admin_email ? 'border-red-500' : ''}
                                        required
                                    />
                                    {errors.admin_email && (
                                        <p className="text-sm text-red-600 mt-1">{errors.admin_email}</p>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="admin_password">Password *</Label>
                                        <Input
                                            id="admin_password"
                                            type="password"
                                            value={data.admin_password}
                                            onChange={(e) => setData('admin_password', e.target.value)}
                                            placeholder="Minimum 8 characters"
                                            className={errors.admin_password ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.admin_password && (
                                            <p className="text-sm text-red-600 mt-1">{errors.admin_password}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="admin_password_confirmation">Confirm Password *</Label>
                                        <Input
                                            id="admin_password_confirmation"
                                            type="password"
                                            value={data.admin_password_confirmation}
                                            onChange={(e) => setData('admin_password_confirmation', e.target.value)}
                                            placeholder="Confirm your password"
                                            className={errors.admin_password_confirmation ? 'border-red-500' : ''}
                                            required
                                        />
                                        {errors.admin_password_confirmation && (
                                            <p className="text-sm text-red-600 mt-1">{errors.admin_password_confirmation}</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Terms and Conditions */}
                            <div className="space-y-4">
                                <div className="flex items-start space-x-2">
                                    <Checkbox
                                        id="terms_accepted"
                                        checked={data.terms_accepted}
                                        onCheckedChange={(checked) => setData('terms_accepted', checked as boolean)}
                                        className={errors.terms_accepted ? 'border-red-500' : ''}
                                    />
                                    <Label htmlFor="terms_accepted" className="text-sm leading-5">
                                        I agree to the{' '}
                                        <a href="/terms" className="text-blue-600 hover:underline" target="_blank">
                                            Terms of Service
                                        </a>{' '}
                                        and{' '}
                                        <a href="/privacy" className="text-blue-600 hover:underline" target="_blank">
                                            Privacy Policy
                                        </a>
                                    </Label>
                                </div>
                                {errors.terms_accepted && (
                                    <p className="text-sm text-red-600">{errors.terms_accepted}</p>
                                )}
                            </div>

                            {/* Error Messages */}
                            {errors.registration && (
                                <Alert variant="destructive">
                                    <AlertDescription>{errors.registration}</AlertDescription>
                                </Alert>
                            )}

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                className="w-full"
                                size="lg"
                                disabled={processing || !data.terms_accepted || slugAvailable === false}
                            >
                                {processing ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating Your Church Account...
                                    </>
                                ) : (
                                    'Create Church Account'
                                )}
                            </Button>
                        </form>

                        <div className="mt-6 text-center">
                            <p className="text-sm text-gray-600">
                                Already have an account?{' '}
                                <a href="/login" className="text-blue-600 hover:underline">
                                    Sign in here
                                </a>
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
}

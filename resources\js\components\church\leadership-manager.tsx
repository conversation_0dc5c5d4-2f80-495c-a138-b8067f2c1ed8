import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { type LeadershipManagerProps } from '@/types/church-settings';

export function LeadershipManager({ 
    formData, 
    onLeadershipChange, 
    onAddLeadership, 
    onRemoveLeadership 
}: LeadershipManagerProps) {
    return (
        <>
            <Separator className="my-6" />

            {/* Leadership Information */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Leadership Information</h3>
                    <Button type="button" variant="outline" onClick={onAddLeadership}>
                        Add Leader
                    </Button>
                </div>

                {formData.leadership_info?.map((leader, index) => (
                    <div key={index} className="space-y-4 rounded-lg border p-4">
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                                <Label htmlFor={`leader_name_${index}`}>Name</Label>
                                <Input
                                    id={`leader_name_${index}`}
                                    value={leader.name}
                                    onChange={(e) => onLeadershipChange(index, 'name', e.target.value)}
                                    placeholder="Pastor John Doe"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor={`leader_position_${index}`}>Position</Label>
                                <Input
                                    id={`leader_position_${index}`}
                                    value={leader.position}
                                    onChange={(e) => onLeadershipChange(index, 'position', e.target.value)}
                                    placeholder="Senior Pastor"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor={`leader_bio_${index}`}>Bio</Label>
                            <Textarea
                                id={`leader_bio_${index}`}
                                value={leader.bio || ''}
                                onChange={(e) => onLeadershipChange(index, 'bio', e.target.value)}
                                placeholder="Brief biography"
                                rows={3}
                            />
                        </div>

                        <div className="flex justify-end">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => onRemoveLeadership(index)}
                                disabled={formData.leadership_info?.length === 1}
                            >
                                Remove Leader
                            </Button>
                        </div>
                    </div>
                ))}
            </div>
        </>
    );
}

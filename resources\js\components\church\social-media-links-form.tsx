import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { type SocialMediaLinksFormProps } from '@/types/church-settings';

export function SocialMediaLinksForm({ formData, onNestedChange }: SocialMediaLinksFormProps) {
    return (
        <>
            <Separator className="my-6" />

            {/* Social Media Links */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">Social Media Links</h3>
                <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                        <Label htmlFor="facebook">Facebook URL</Label>
                        <Input
                            id="facebook"
                            type="url"
                            value={formData.social_media_links?.facebook || ''}
                            onChange={(e) => onNestedChange('social_media_links', 'facebook', e.target.value)}
                            placeholder="https://facebook.com/yourchurch"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="twitter">Twitter/X URL</Label>
                        <Input
                            id="twitter"
                            type="url"
                            value={formData.social_media_links?.twitter || ''}
                            onChange={(e) => onNestedChange('social_media_links', 'twitter', e.target.value)}
                            placeholder="https://twitter.com/yourchurch"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="instagram">Instagram URL</Label>
                        <Input
                            id="instagram"
                            type="url"
                            value={formData.social_media_links?.instagram || ''}
                            onChange={(e) => onNestedChange('social_media_links', 'instagram', e.target.value)}
                            placeholder="https://instagram.com/yourchurch"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="youtube">YouTube URL</Label>
                        <Input
                            id="youtube"
                            type="url"
                            value={formData.social_media_links?.youtube || ''}
                            onChange={(e) => onNestedChange('social_media_links', 'youtube', e.target.value)}
                            placeholder="https://youtube.com/yourchurch"
                        />
                    </div>
                </div>
            </div>
        </>
    );
}

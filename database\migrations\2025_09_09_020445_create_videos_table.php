<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('videos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tenant_id')->nullable();
            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->string('speaker')->nullable();
            $table->date('recorded_date')->nullable();
            $table->json('scripture_references')->nullable(); // Array of scripture references
            $table->enum('type', ['sermon', 'testimony', 'announcement', 'event', 'other'])->default('sermon');

            // File storage information
            $table->string('file_path'); // Path to video file
            $table->string('thumbnail_path')->nullable(); // Path to thumbnail image
            $table->string('original_filename');
            $table->string('mime_type');
            $table->bigInteger('file_size'); // File size in bytes
            $table->integer('duration')->nullable(); // Duration in seconds
            $table->json('video_metadata')->nullable(); // Resolution, codec, etc.

            // Content organization
            $table->foreignId('category_id')->nullable()->constrained('content_categories')->onDelete('set null');
            $table->string('series_name')->nullable(); // For sermon series
            $table->integer('series_order')->nullable();

            // Publishing and visibility
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');

            // Analytics
            $table->integer('view_count')->default(0);
            $table->integer('download_count')->default(0);

            $table->timestamps();

            // Composite unique constraint for slug + tenant_id
            $table->unique(['slug', 'tenant_id']);

            // Indexes for performance
            $table->index('tenant_id');
            $table->index(['status', 'published_at']);
            $table->index(['type', 'status']);
            $table->index(['category_id', 'status']);
            $table->index(['series_name', 'series_order']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('videos');
    }
};
